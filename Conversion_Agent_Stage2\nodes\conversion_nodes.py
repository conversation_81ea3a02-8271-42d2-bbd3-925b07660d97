# Standard library imports
import os
import pandas as pd
from typing import Dict, Any, List, Optional
import shutil
import importlib.util
from datetime import datetime
import json
import time

# Local imports - State
from Conversion_Agent_Stage2.state import (
    Stage2WorkflowState,
    ResponsibleFeaturesAnalysisOutput,
    ModuleEnhancementOutput,
    StatementComparisonOutput
)
from config import Config
from common.database import get_qbook_db_config, insert_stage2_conversion_module, stage2_statement_features_update, insert_update_statement_attempt, get_encryption_key_for_migration, cleanup_all_statement_attempts, update_stage2_conversion_module_status
from Conversion_Agent_Stage2.qmigrator_conversion.object_conversion import qbook_object_conversion, decrypt_conversion_file, replace_comment_markers
from Conversion_Agent_Stage2.logger.custom_logger import Stage2PipelineLogger
from Conversion_Agent_Stage2.prompts.responsible_features_identification_prompt import (
    create_responsible_features_identification_prompt
)

from Conversion_Agent_Stage2.prompts.sequential_enhancement_prompt import (
    create_sequential_enhancement_prompt
)
from Conversion_Agent_Stage2.utils.database_names import get_database_specific_terms

# Registry utilities
from Conversion_Agent_Stage2.utils.registry_utils import (
    check_registry_for_module,
    load_enhanced_module_from_registry,
    save_enhanced_module_to_registry,
    get_registry_path
)
from Conversion_Agent_Stage2.prompts.ai_statement_comparison_prompt import (
    create_ai_statement_comparison_prompt
)
from common.comments_handling import remove_comments_for_comparison


def get_stage2_excel_path(metadata_dir: str, schema_name: str, object_name: str) -> str:
    """Get the path for the Stage 2 workflow tracking Excel file."""
    filename = f"{schema_name}_{object_name}_Stage2.xlsx"
    return os.path.join(metadata_dir, filename)

def create_excel_with_sheet(file_path: str, sheet_name: str, data: List[Dict[str, Any]]) -> str:
    """
    Generic function to create new Excel file with first sheet.

    Args:
        file_path: Full path to Excel file
        sheet_name: Name of the sheet to create
        data: List of dictionaries containing sheet data

    Returns:
        str: Path to the created Excel file
    """
    try:
        # Sanitize data before creating DataFrame
        sanitized_data = sanitize_excel_data(data)
        df = pd.DataFrame(sanitized_data)
        with pd.ExcelWriter(file_path, mode='w', engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)
        return file_path
    except Exception as e:
        print(f"❌ Error creating Excel file {file_path}: {e}")
        return ""

def sanitize_excel_data(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Sanitize data for Excel compatibility by handling special characters and long text.

    Args:
        data: List of dictionaries containing data to sanitize

    Returns:
        List[Dict[str, Any]]: Sanitized data safe for Excel
    """
    def sanitize_text(text: Any) -> str:
        """Sanitize individual text values for Excel compatibility."""
        if text is None:
            return ""

        text_str = str(text)

        # Excel cell limit is 32,767 characters
        if len(text_str) > 32000:
            text_str = text_str[:32000] + "... [TRUNCATED]"

        # Replace problematic characters that Excel considers invalid
        # These are characters that commonly cause "cannot be used in worksheets" errors
        replacements = {
            '\x00': '',  # Null character
            '\x01': '',  # Start of heading
            '\x02': '',  # Start of text
            '\x03': '',  # End of text
            '\x04': '',  # End of transmission
            '\x05': '',  # Enquiry
            '\x06': '',  # Acknowledge
            '\x07': '',  # Bell
            '\x08': '',  # Backspace
            '\x0B': '',  # Vertical tab
            '\x0C': '',  # Form feed
            '\x0E': '',  # Shift out
            '\x0F': '',  # Shift in
            '\x10': '',  # Data link escape
            '\x11': '',  # Device control 1
            '\x12': '',  # Device control 2
            '\x13': '',  # Device control 3
            '\x14': '',  # Device control 4
            '\x15': '',  # Negative acknowledge
            '\x16': '',  # Synchronous idle
            '\x17': '',  # End of transmission block
            '\x18': '',  # Cancel
            '\x19': '',  # End of medium
            '\x1A': '',  # Substitute
            '\x1B': '',  # Escape
            '\x1C': '',  # File separator
            '\x1D': '',  # Group separator
            '\x1E': '',  # Record separator
            '\x1F': '',  # Unit separator
        }

        for old_char, new_char in replacements.items():
            text_str = text_str.replace(old_char, new_char)

        # Handle other potential issues
        # Remove any remaining control characters (except tab, newline, carriage return)
        import re
        text_str = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text_str)

        return text_str

    sanitized_data = []
    for row in data:
        sanitized_row = {}
        for key, value in row.items():
            sanitized_row[key] = sanitize_text(value)
        sanitized_data.append(sanitized_row)

    return sanitized_data



def append_sheet_to_excel(file_path: str, sheet_name: str, data: List[Dict[str, Any]]) -> bool:
    """
    Generic function to append data to existing Excel sheet without reading full content.

    Args:
        file_path: Full path to Excel file
        sheet_name: Name of the sheet to append
        data: List of dictionaries containing sheet data

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Sanitize data before creating DataFrame
        sanitized_data = sanitize_excel_data(data)
        df = pd.DataFrame(sanitized_data)

        if not os.path.exists(file_path):
            # Create new file
            with pd.ExcelWriter(file_path, mode='w', engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        else:
            # Append to existing file - add rows to the bottom of existing sheet
            with pd.ExcelWriter(file_path, mode='a', if_sheet_exists='overlay', engine='openpyxl') as writer:
                # Check if sheet exists to determine start row
                if sheet_name in writer.book.sheetnames:
                    existing_sheet = writer.book[sheet_name]
                    start_row = existing_sheet.max_row  # Start after last row
                    df.to_excel(writer, sheet_name=sheet_name, index=False, header=False, startrow=start_row)
                else:
                    # Sheet doesn't exist, create new with headers
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

        return True
    except Exception as e:
        print(f"❌ Error appending sheet {sheet_name} to {file_path}: {e}")
        return False

class Stage2ProcessingNodes:
    """
    Stage 2 processing nodes for QMigrator module updates.

    This class provides all workflow nodes for the complete Stage 2 processing
    with a streamlined 9-node pipeline approach. Handles both qmigrator (object-level)
    and qbook (statement-level) processing with comprehensive retry mechanisms.

    Core Workflow Nodes:
        1. Process Type Decision - Routes between qmigrator/qbook paths
        2. Post Stage1 Processing (QMigrator) - Object-level processing
        3. Map Feature Combinations (QMigrator) - Feature mapping
        4. Statement Level Processing (QBook) - Statement-level processing
        5. Identify Responsible Features - AI-driven feature identification

    9-Node Pipeline (Enhanced Module Processing):
        6. Categorize Execution Modules - Pre/responsible/post categorization
        7. Execute Pre Features - Pre-processing execution
        8. Combine Driver Module - Module combination
        9. Enhance Driver Module - AI-driven enhancement
        10. Decompose Enhanced Module - Module decomposition
        11. Validate Module Enhancement - Validation with retry
        12. Execute Complete Pipeline - Full pipeline execution with retry
        13. AI Statement Comparison - Functional equivalence analysis
        14. Enhancement Iteration Control - Retry coordination

    Additional Nodes:
        - More Statements Decision - Statement processing loop control
        - Complete Processing - Workflow finalization

    Workflow Integration:
        Designed for use with LangGraph workflow orchestration, providing seamless
        state management and conditional routing based on validation results.
        Includes comprehensive Excel logging and retry mechanisms.
    """

    def __init__(self, llm):
        """
        Initialize the Stage 2 processing nodes with AI language model integration.

        Sets up the node collection with the provided language model for AI-driven
        analysis throughout the Stage 2 module update workflow.

        Args:
            llm: Initialized Language Model instance supporting structured outputs
                 for reliable AI analysis and module update operations. Compatible with
                 multiple providers (OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama).

        Attributes:
            llm: The language model instance used across all Stage 2 nodes
        """
        self.llm = llm

    # ==================== WORKFLOW DECISION NODES ====================

    def process_type_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 1: Process Type Decision - Workflow Router

        Purpose:
            Routes the workflow to appropriate processing path based on the process_type parameter.
            This is the entry point that determines whether to use QMigrator (object-level) or
            QBook (statement-level) processing approach.

        Business Logic:
            - QMigrator: Object-level processing for complete database objects (procedures, functions)
            - QBook: Statement-level processing for individual SQL statements

        Input Requirements:
            - state.process_type: Must be either "qmigrator" or "qbook"

        Output:
            - process_type: Confirmed process type for downstream routing

        Next Nodes:
            - qmigrator → post_stage1_processing_qmigrator
            - qbook → statement_level_processing_qbook

        Error Handling:
            - No validation errors expected as process_type is set during workflow initialization
        """
        print(f"🔀 Process Type Decision: {state.process_type}")

        if state.process_type == "qmigrator":
            print("📁 Routing to QMigrator object-level processing")
        else:
            print("📝 Routing to QBook statement-level processing")

        # Return state update - routing is handled by conditional edges
        return {
            "process_type": state.process_type
        }

    # ==================== QMIGRATOR PATH NODES ====================

    def post_stage1_processing_qmigrator(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 2: Post-Stage 1 Processing - QMigrator Object Conversion

        Purpose:
            Processes Stage 1 approved statements through QMigrator object-level conversion.
            This node handles complete database objects (procedures, functions, triggers)
            by reading approved statements and running comprehensive QMigrator conversion.

        Business Logic:
            1. Reads approved_statements.csv from Stage 1 metadata directory
            2. Loads source_code.sql containing the original object code
            3. Executes QMigrator object-level conversion with full feature processing
            4. Generates conversion results including statements, features, and deployment errors

        Input Requirements:
            - state.migration_name: Migration identifier for path construction
            - state.schema_name: Database schema name
            - state.object_name: Database object name
            - state.objecttype: Object type (procedure, function, trigger, etc.)
            - state.cloud_category: Deployment environment (cloud/local)

        Output:
            - qmigrator_results_df: DataFrame with conversion results
            - approved_statements_df: DataFrame with approved statements
            - source_code: Original SQL source code
            - conversion_success: Boolean indicating conversion status

        Next Nodes:
            - Success → map_feature_combinations
            - Failure → complete_processing (with error)

        Error Handling:
            - File not found errors for missing Stage 1 outputs
            - QMigrator conversion failures
            - DataFrame processing errors
        """
        print("\n" + "="*80)
        print("📁 POST-STAGE 1 PROCESSING (QMIGRATOR)")
        print("="*80)
        print(f"🎯 Purpose: Object-level conversion using QMigrator for complete database objects")
        print(f"🔧 Migration: {state.migration_name}")
        print(f"🏗️ Target: {state.schema_name}.{state.object_name} ({state.objecttype})")
        print(f"☁️ Environment: {state.cloud_category}")

        try:
            # Clean process-type-specific feature modules directory for fresh start when reprocessing
            self.cleanup_process_feature_modules(state)
            # Determine paths based on cloud_category
            if state.cloud_category.lower() == "local":
                qbook_root_path = Config.Qbook_Local_Path
                temp_root_path = Config.Temp_Local_Path
            else:
                qbook_root_path = Config.Qbook_Path
                temp_root_path = Config.Temp_Path

            # Construct metadata directory path
            metadata_dir = os.path.join(
                qbook_root_path,
                'Stage1_Metadata',
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name
            )

            # Create temp directory for Stage 2 processing
            temp_dir = os.path.join(
                temp_root_path,
                'Stage2_Processing',
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name
            )
            os.makedirs(temp_dir, exist_ok=True)

            approved_statements_path = os.path.join(metadata_dir, 'approved_statements.csv')
            source_code_path = os.path.join(metadata_dir, 'source_code.sql')

            print(f"\n📂 DIRECTORY SETUP:")
            print(f"   📁 Metadata: {metadata_dir}")
            print(f"   🗂️ Temp: {temp_dir}")
            print(f"   📄 Approved Statements: {approved_statements_path}")
            print(f"   📜 Source Code: {source_code_path}")

            # 3. Read approved statements CSV
            if not os.path.exists(approved_statements_path):
                raise FileNotFoundError(f"Approved statements file not found: {approved_statements_path}")

            approved_statements_df = pd.read_csv(approved_statements_path)
            print(f"\n📊 STAGE 1 DATA LOADING:")
            print(f"   ✅ Approved Statements: {len(approved_statements_df)} statements loaded")

            # 4. Read source code file
            if not os.path.exists(source_code_path):
                raise FileNotFoundError(f"Source code file not found: {source_code_path}")

            with open(source_code_path, 'r', encoding='utf-8') as f:
                source_code_content = f.read()

            if not source_code_content.strip():
                raise ValueError("Source code file is empty")

            print(f"   ✅ Source Code: {len(source_code_content)} characters loaded")

            # 5. Run QMigrator object-level conversion
            print(f"\n🔄 QMIGRATOR OBJECT CONVERSION:")
            print(f"   🎯 Converting {state.objecttype}: {state.schema_name}.{state.object_name}")
            print(f"   ⚙️ Processing with full feature analysis...")

            object_converted_output, available_features_df, comment_dict = qbook_object_conversion(
                migration_name=state.migration_name,
                schema_name=state.schema_name,
                object_type=state.objecttype,
                object_name=state.object_name,
                source_data=source_code_content,
                cloud_category=state.cloud_category
            )
            
            if object_converted_output is None:
                raise ValueError("QMigrator conversion failed - no output generated")

            print(f"   ✅ QMigrator conversion completed successfully")
            print(f"   📊 Generated: {len(available_features_df)} statement-feature mappings")
            print(f"   💬 Comments: {len(comment_dict)} comment markers preserved")

            # Create Stage 2 Excel tracking file in temp directory
            stage2_excel_path = get_stage2_excel_path(temp_dir, state.schema_name, state.object_name)
            final_qbook_excel_path = get_stage2_excel_path(metadata_dir, state.schema_name, state.object_name)
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            # Prepare data for Excel sheets
            source_code_data = [{
                "Migration_Name": state.migration_name,
                "Schema_Name": state.schema_name,
                "Object_Name": state.object_name,
                "Object_Type": state.objecttype,
                "Source_Code": source_code_content,
                "Timestamp": current_timestamp
            }]

            approved_statements_data = []
            for idx, row in approved_statements_df.iterrows():
                approved_statements_data.append({
                    "Migration_Name": row.get('migration_name', state.migration_name),
                    "Schema_Name": row.get('schema_name', state.schema_name),
                    "Object_Name": row.get('object_name', state.object_name),
                    "Object_Type": row.get('object_type', state.objecttype),
                    "TGT_Object_ID": row.get('tgt_object_id', ''),
                    "Source_Statement_Number": row.get('source_statement_number', ''),
                    "Target_Statement_Number": row.get('target_statement_number', ''),
                    "Original_Source_Statement": str(row.get('original_source_statement', '')),
                    "Original_Target_Statement": str(row.get('original_target_statement', '')),
                    "AI_Converted_Statement": str(row.get('ai_converted_statement', '')),  # Contains best available statement
                    "Original_Deployment_Error": str(row.get('original_deployment_error', '')),  # Contains best available error
                    "Timestamp": current_timestamp
                })

            available_features_data = available_features_df.copy()
            available_features_data['Timestamp'] = current_timestamp
            available_features_list = available_features_data.to_dict('records')

            # Create Excel file with three sheets
            create_excel_with_sheet(stage2_excel_path, "Source_Code", source_code_data)
            # Small delay to prevent Excel file corruption
            time.sleep(0.1)
            append_sheet_to_excel(stage2_excel_path, "Approved_Statements", approved_statements_data)
            # Small delay to prevent Excel file corruption
            time.sleep(0.1)
            append_sheet_to_excel(stage2_excel_path, "Available_Features", available_features_list)
            print(f"📋 Sheet 'Available_Features' added with {len(available_features_list)} records")

            print("✅ Post-Stage 1 Processing (QMigrator) completed successfully")
            print(f"📊 Total approved statements: {len(approved_statements_df)}")
            print(f"📊 Total available features: {len(available_features_df)}")

            # 7. Return state fields for LangGraph persistence
            return {
                "approved_statements": approved_statements_df.to_dict('records'),
                "object_level_features": available_features_df.to_dict('records'),
                "source_code": source_code_content,
                "stage2_excel_path": stage2_excel_path,
                "final_qbook_excel_path": final_qbook_excel_path,
                "metadata_dir": metadata_dir,
                "temp_dir": temp_dir,
                "approved_statements_df": approved_statements_df,
                "available_features_df": available_features_df,
                "object_converted_output": object_converted_output,
                "comments_dict": comment_dict
            }

        except FileNotFoundError as e:
            error_msg = f"❌ File not found: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "approved_statements": [],
                "object_level_features": [],
                "source_code": "",
                "stage2_excel_path": "",
                "final_qbook_excel_path": "",
                "metadata_dir": "",
                "temp_dir": "",
                "approved_statements_df": pd.DataFrame(),
                "available_features_df": pd.DataFrame(),
                "object_converted_output": "",
                "comments_dict": {}
            }

        except Exception as e:
            error_msg = f"❌ Processing failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "approved_statements_df": pd.DataFrame(),
                "available_features_df": pd.DataFrame(),
                "comments_dict": {},
                "source_code": "",
                "object_converted_output": "",
                "stage2_excel_path": "",
                "final_qbook_excel_path": "",
                "metadata_dir": "",
                "temp_dir": ""
            }

    def map_feature_combinations(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 3: Map Feature Combinations Node (QMigrator Only).

        Purpose: Map approved statements with object-level features based on statement number matching.
        Only creates combined entries when statement numbers match between approved statements and available features.

        Process:
            1. Get approved statements and available features from workflow state
            2. Map statements only when source_statement_number matches Statement_Number
            3. Create combined dataset for feature analysis with matching entries only
            4. Create Excel sheet with combined data

        Args:
            state: Stage2WorkflowState containing approved_statements and object_level_features

        Returns:
            Dict containing available_features_with_statements and excel_path
        """
        print("🔄 Starting Map Feature Combinations...")

        try:
            # Get DataFrames from workflow state
            if not state.approved_statements or not state.object_level_features:
                raise ValueError("Missing approved statements or object level features from previous step")

            # Convert state data back to DataFrames for processing
            approved_statements_df = pd.DataFrame(state.approved_statements)
            available_features_df = pd.DataFrame(state.object_level_features)

            print(f"📊 Processing {len(approved_statements_df)} approved statements")
            print(f"📊 Processing {len(available_features_df)} available features")

            # Create combined dataset only for matching statement numbers
            combined_features = []
            statement_id_counter = 1

            for _, approved_stmt in approved_statements_df.iterrows():
                # Find matching features by statement number
                source_stmt_num = approved_stmt.get('source_statement_number', 0)
                matching_features = available_features_df[
                    available_features_df['Statement_Number'] == source_stmt_num
                ]

                # Only create combined entry if matching features exist
                if not matching_features.empty:
                    feature_row = matching_features.iloc[0]
                    combined_entry = {
                        # Combined dataset ID
                        "statement_id": statement_id_counter,

                        # From approved statements
                        "migration_name": approved_stmt.get('migration_name', ''),
                        "schema_name": approved_stmt.get('schema_name', ''),
                        "object_name": approved_stmt.get('object_name', ''),
                        "object_type": approved_stmt.get('object_type', ''),
                        "tgt_object_id": approved_stmt.get('tgt_object_id', 0),
                        "source_statement_number": approved_stmt.get('source_statement_number', 0),
                        "target_statement_number": approved_stmt.get('target_statement_number', 0),
                        "original_source_statement": approved_stmt.get('original_source_statement', ''),
                        "original_target_statement": approved_stmt.get('original_target_statement', ''),
                        "ai_converted_statement": approved_stmt.get('ai_converted_statement', ''),
                        "original_deployment_error": approved_stmt.get('original_deployment_error', ''),

                        # From available features
                        "statement_after_typecasting": feature_row.get('Statement_After_Typecasting', ''),
                        "statement_level_output": feature_row.get('Statement_Level_Output', ''),
                        "available_features": feature_row.get('Available_Features', []),
                        "post_features": feature_row.get('Post_Features', [])
                    }
                    combined_features.append(combined_entry)
                    statement_id_counter += 1
                else:
                    print(f"⚠️ No matching features found for approved statement {source_stmt_num} - excluding from combined dataset")
                
            # Add Combined_Features sheet to existing Excel file using existing function
            if hasattr(state, 'stage2_excel_path') and state.stage2_excel_path:
                # Prepare combined data with timestamp
                combined_data = []
                current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

                if combined_features:
                    for feature in combined_features:
                        combined_record = feature.copy()
                        combined_record['Timestamp'] = current_timestamp
                        combined_data.append(combined_record)
                else:
                    # Add empty record if no combined features
                    combined_data = [{"Message": "No matching features found", "Timestamp": current_timestamp}]

                # Use existing append_sheet_to_excel function
                append_sheet_to_excel(state.stage2_excel_path, "Combined_Features", combined_data)
                print(f"📋 Sheet 'Combined_Features' added with {len(combined_data)} records")
            else:
                print("⚠️ Stage 2 Excel file path not found in state - cannot add Combined_Features sheet")

            print("✅ Map Feature Combinations completed successfully")
            print(f"📊 Total matched combinations: {len(combined_features)}")

            return {
                "available_features_with_statements": combined_features
            }

        except Exception as e:
            error_msg = f"❌ Map Feature Combinations failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "available_features_with_statements": []
            }
    # ==================== QBOOK PATH NODES ====================

    def statement_level_processing_qbook(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 2: Statement Level Processing Node (QBook Path).

        Purpose: Process individual statements using QBook statement-level conversion.

        Process:
            Run QMigrator statement-level conversion on single statement
            Return converted statement for workflow processing

        Args:
            state: Stage2WorkflowState containing statement context

        Returns:
            Dict containing converted statement results
        """
        print("🔄 Starting Statement Level Processing (QBook)...")

        try:
            print("⚠️ QBook statement-level processing not yet implemented")
            print("✅ Statement Level Processing (QBook) completed successfully")

            return {
                "converted_statement": "",
                "responsible_features": [],
                "responsible_procedures": []
            }

        except Exception as e:
            error_msg = f"❌ Statement Level Processing (QBook) failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "converted_statement": "",
                "responsible_features": [],
                "responsible_procedures": []
            }

    # ==================== FEATURE IDENTIFICATION NODES ====================

    def identify_responsible_features(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 5: Identify Responsible Features - AI-Driven Root Cause Analysis

        Purpose:
            Uses AI analysis to identify specific Python conversion modules responsible for
            conversion failures by comparing expected vs actual conversion results.
            This is the critical diagnostic node that determines which modules need enhancement.

        Business Logic:
            1. Analyzes conversion discrepancies between AI output and expected target
            2. Reads and decrypts available Python conversion modules
            3. Uses AI to correlate conversion failures with specific module responsibilities
            4. Maps Oracle-to-PostgreSQL keywords to identify relevant modules
            5. Provides detailed responsibility reasoning for each identified module

        AI Analysis Process:
            - Compares original_source_statement vs ai_converted_statement vs original_target_statement
            - Identifies functional gaps and conversion errors
            - Maps errors to specific module capabilities using keyword analysis
            - Provides actionable responsibility reasons for module enhancement

        Input Requirements:
            - state.available_features_with_statements: Combined statement and feature data
            - state.current_statement_index: Current statement being processed (0-based)
            - state.migration_name: For module path construction
            - state.cloud_category: For environment-specific paths

        Output:
            - responsible_features: List of (feature_name, module_path, responsibility_reason, keywords)
            - analysis_summary: AI analysis summary
            - keyword_matches: Keyword-to-module mappings found

        Next Nodes:
            - Success → categorize_execution_modules (9-node pipeline entry)
            - No features found → complete_processing

        Error Handling:
            - Missing statement data validation
            - Module decryption failures
            - AI analysis errors with fallback responses
        """
        print("\n" + "="*80)
        print("🔍 IDENTIFY RESPONSIBLE FEATURES")
        print("="*80)

        try:
            # Get combined data and current statement index from state
            if not state.available_features_with_statements:
                raise ValueError("Missing available_features_with_statements from previous step")

            combined_data = state.available_features_with_statements
            current_statement_index = getattr(state, 'current_statement_index', 0)

            # Get the current statement to process
            if current_statement_index >= len(combined_data):
                raise ValueError(f"Current statement index {current_statement_index} exceeds available statements {len(combined_data)}")

            current_statement_data = combined_data[current_statement_index]
            statement_id = current_statement_data.get('statement_id', current_statement_index + 1)
            source_stmt_num = current_statement_data.get('source_statement_number', 'Unknown')

            print(f"🎯 Purpose: AI-driven root cause analysis to identify modules responsible for conversion failures")
            print(f"📊 Processing: Statement {current_statement_index + 1}/{len(combined_data)} (Source #{source_stmt_num})")
            print(f"🔍 STATEMENT ANALYSIS:")
            print(f"   📝 Statement ID: {statement_id}")
            print(f"   📝 Source Statement Number: {source_stmt_num}")
            print(f"   📝 Position: {current_statement_index + 1}/{len(combined_data)}")

            # Extract key statement information for analysis
            original_source = current_statement_data.get('original_source_statement', '')
            ai_converted = current_statement_data.get('ai_converted_statement', '')
            actual_target = current_statement_data.get('original_target_statement', '')
            deployment_error = current_statement_data.get('original_deployment_error', '')

            print(f"🔍 Oracle Source: {original_source}")
            print(f"📝 Source Statement After Typecasting: {current_statement_data.get('statement_after_typecasting', '')}")
            print(f"🎯 Expected PostgreSQL: {ai_converted}")
            print(f"❌ Actual PostgreSQL: {actual_target}")
            if deployment_error:
                print(f"⚠️ Deployment Error: {deployment_error}")

            # Get migration name and cloud category from state (Request-First Approach)
            migration_name = state.migration_name
            cloud_category = getattr(state, 'cloud_category', 'cloud')  # Default to 'cloud' if not found
            print(f"🔧 Using dynamic migration name: {migration_name}")
            print(f"☁️ Using cloud category: {cloud_category}")

            # Load keyword mapping dynamically based on migration_name and cloud_category
            keyword_mapping = self.load_keyword_mapping(migration_name, cloud_category)

            # Get complete pipeline features for comprehensive AI analysis
            available_features = current_statement_data.get("available_features", [])
            post_features = current_statement_data.get("post_features", [])
            complete_available_features = available_features + post_features

            print(f"🔍 Complete features for AI analysis: {len(complete_available_features)} modules")
            print(f"   📋 Available features: {available_features}")
            print(f"   📤 Post features: {post_features}")
            print(f"   🔗 Complete pipeline: {complete_available_features}")

            module_paths = self.get_module_paths(
                complete_available_features,
                current_statement_data.get("migration_name", migration_name),
                cloud_category
            )

            # Decrypt and read Python modules for this statement
            decrypted_modules = self.decrypt_and_read_modules(module_paths, migration_name)
            print(f"🔓 Decrypted {len(decrypted_modules)} modules for analysis")

            # Get validation feedback if available
            validation_feedback = getattr(state, 'validation_feedback', None)
            if validation_feedback:
                print(f"🔄 Using validation feedback for improved identification...")
                print(f"📝 Feedback: {validation_feedback}")

            # AI analysis to identify responsible modules for this specific statement
            print(f"\n🧠 AI ROOT CAUSE ANALYSIS:")
            print(f"   🎯 Analyzing conversion discrepancies and module responsibilities")
            print(f"   📊 Available modules: {len(decrypted_modules)}")
            print(f"   🗝️ Keyword mappings: {len(keyword_mapping)}")
            print(f"   🔍 Starting AI analysis...")

            analysis_result = self.ai_analyze_responsible_modules(
                current_statement_data, decrypted_modules, keyword_mapping, migration_name, validation_feedback, complete_available_features
            )

            # Extract results
            responsible_features = analysis_result.get("responsible_features", [])
            analysis_summary = analysis_result.get("analysis_summary", "No analysis summary available")

            # Log results for this statement
            print(f"\n✅ AI ANALYSIS RESULTS:")
            print(f"   📊 Responsible modules identified: {len(responsible_features)}")

            if responsible_features:
                module_names = [feature[0] for feature in responsible_features]
                print(f"   🎯 Modules requiring enhancement: {', '.join(module_names)}")
                print(f"   📝 Analysis summary: {analysis_summary}")
            else:
                print("   ✅ No responsible modules - conversion working correctly")

            # Log to Excel (optional - for tracking purposes)
            if hasattr(state, 'stage2_excel_path') and state.stage2_excel_path:
                current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

                # Format responsible features for Excel logging
                features_for_excel = []

                for feature_data in responsible_features:
                    if len(feature_data) == 4:
                        feature_name, module_path, responsibility_reason, keywords = feature_data
                        features_for_excel.append(f"{feature_name} | {module_path} | Responsibility: {responsibility_reason} | Keywords: {keywords}")
                    elif len(feature_data) == 3:
                        feature_name, module_path, responsibility_reason = feature_data
                        features_for_excel.append(f"{feature_name} | {module_path} | Responsibility: {responsibility_reason} | Keywords: N/A")
                    elif len(feature_data) >= 2:
                        feature_name, module_path = feature_data[0], feature_data[1]
                        features_for_excel.append(f"{feature_name} | {module_path} | Responsibility: N/A | Keywords: N/A")
                    else:
                        features_for_excel.append(f"Invalid feature data: {feature_data}")

                # Create log entry
                log_entry = {
                    "Statement_ID": statement_id,
                    "Source_Statement_Number": source_stmt_num,
                    "Responsible_Modules_Count": len(responsible_features),
                    "Responsible_Features": features_for_excel,
                    "Analysis": analysis_summary,
                    "Processing_Attempt": getattr(state, 'current_attempt', 1),
                    "Timestamp": current_timestamp
                }

                append_sheet_to_excel(state.stage2_excel_path, "Responsible_Features", [log_entry])
                print(f"📋 Logged source statement {source_stmt_num} analysis")

                # Additional pipeline overview logging
                if responsible_features:
                    # Log successful identification
                    self.log_pipeline_overview(state.stage2_excel_path, {
                        'statement_number': current_statement_index + 1,
                        'source_statement_number': source_stmt_num,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'total_modules': 0,
                        'pre_count': 0,
                        'responsible_count': len(responsible_features),
                        'post_count': 0,
                        'overall_status': 'RESPONSIBLE_FEATURES_IDENTIFIED',
                        'pipeline_phase': 'Feature Identification',
                        'success': True,
                        'notes': f'AI identified {len(responsible_features)} responsible modules: {", ".join([feature[0] for feature in responsible_features])}'
                    })
                else:
                    # Log no features found
                    self.log_pipeline_overview(state.stage2_excel_path, {
                        'statement_number': current_statement_index + 1,
                        'source_statement_number': source_stmt_num,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'total_modules': 0,
                        'pre_count': 0,
                        'responsible_count': 0,
                        'post_count': 0,
                        'overall_status': 'NO_RESPONSIBLE_FEATURES',
                        'pipeline_phase': 'Feature Identification',
                        'success': False,
                        'notes': 'AI could not identify any modules responsible for conversion issues - statement will be skipped'
                    })

            print(f"\n✅ Identify Responsible Features completed for source statement {source_stmt_num}")
            print(f"📊 responsible modules identified: {responsible_features}")

            # Update statement features instead of full insert
            try:
                # Get post features for responsible_program_level_features
                post_features = self.get_post_processing_features(current_statement_data)

                # Update statement features for existing record
                statement_id = stage2_statement_features_update(
                    db_data=get_qbook_db_config(),
                    process_type=state.process_type,
                    migration_name=state.migration_name,
                    schema_name=state.schema_name,
                    object_type=state.objecttype,
                    tgt_object_name=state.object_name,
                    target_statement_number=current_statement_data.get('target_statement_number'),
                    source_before_statement_level=current_statement_data.get('statement_after_typecasting', ''),
                    source_after_statement_level=current_statement_data.get('statement_level_output', ''),
                    statement_level_features=json.dumps(current_statement_data.get('available_features', [])),
                    program_level_features=json.dumps(current_statement_data.get('post_features', [])),
                    responsible_statement_level_features=json.dumps(responsible_features),
                    responsible_program_level_features=json.dumps(post_features)
                )

                if statement_id:
                    print(f"✅ Successfully updated statement features in database with ID: {statement_id}")

                    # 🧹 PRE-PROCESSING CLEANUP: Clean up any existing attempts data for this statement
                    print(f"🧹 Performing pre-processing cleanup for statement_id={statement_id}")
                    cleanup_success = cleanup_all_statement_attempts(
                        db_data=get_qbook_db_config(),
                        statement_id=statement_id
                    )
                    if cleanup_success:
                        print(f"✅ Pre-processing cleanup completed for statement {source_stmt_num}")
                    else:
                        print(f"⚠️ Pre-processing cleanup encountered issues for statement {source_stmt_num} (non-critical)")
                else:
                    print("⚠️ Failed to update statement features in database")

            except Exception as db_error:
                print(f"⚠️ Database features update error: {str(db_error)}")
                statement_id = None  # Set to None if database update fails

            # NEW: Registry check and module loading
            registry_status = "none_in_registry"
            registry_modules = []
            missing_modules = []
            enhanced_modules_loaded = False

            if responsible_features:
                print(f"🗂️ Checking registry for {len(responsible_features)} responsible modules...")
                registry_status, registry_modules, missing_modules = self.check_and_load_modules_with_registry(
                    responsible_features, migration_name, cloud_category
                )
                enhanced_modules_loaded = registry_status in ["all_in_registry", "some_in_registry"]


                print(f"📊 Registry Status: {registry_status}")
                print(f"🗂️ Enhanced modules available: {len(registry_modules)}")
                print(f"📁 Original modules required: {len(missing_modules)}")

            # Return responsible features and statement_id without clearing feedback
            # Feedback should only be cleared by the nodes that consume it (enhance_driver_module)
            return {
                "responsible_features": responsible_features,
                "current_statement_id": statement_id,  # Add statement_id to state
                # Registry integration fields
                "registry_status": registry_status,
                "registry_modules": registry_modules,
                "missing_modules": missing_modules,
                "enhanced_modules_loaded": enhanced_modules_loaded,
                "module_source": "enhanced" if enhanced_modules_loaded else "original"
            }

        except Exception as e:
            error_msg = f"❌ Identify Responsible Features failed: {str(e)}"
            print(error_msg)
            return {
                "responsible_features": [],  # Return empty list on error
                "error": error_msg
            }

    # ==================== HELPER METHODS ====================

    def check_and_load_modules_with_registry(self, responsible_features: List[tuple], migration_name: str, cloud_category: str) -> tuple:
        """
        Check registry and load enhanced/original modules.

        Args:
            responsible_features: List of (feature_name, module_path, ...) tuples
            migration_name: Migration name for registry path
            cloud_category: Cloud category for path construction

        Returns:
            Tuple of (registry_status, registry_modules, missing_modules)
        """
        try:
            registry_modules = []
            missing_modules = []

            for feature_data in responsible_features:
                feature_name = feature_data[0]
                module_path = feature_data[1]

                # Check if enhanced version exists in registry
                if check_registry_for_module(module_path, migration_name, cloud_category):
                    # Keep the full tuple structure for registry modules
                    registry_modules.append(feature_data)
                    print(f"✅ Enhanced module found in registry: {feature_name}")
                else:
                    # Keep the full tuple structure for missing modules
                    missing_modules.append(feature_data)
                    print(f"📁 Original module required: {feature_name}")

            # Determine registry status
            total_modules = len(responsible_features)
            enhanced_count = len(registry_modules)

            if enhanced_count == total_modules and total_modules > 0:
                status = "all_in_registry"
            elif enhanced_count > 0:
                status = "some_in_registry"
            else:
                status = "none_in_registry"

            print(f"📊 Registry analysis complete:")
            print(f"   - Total modules: {total_modules}")
            print(f"   - Enhanced modules available: {enhanced_count}")
            print(f"   - Original modules required: {len(missing_modules)}")
            print(f"   - Status: {status}")

            return status, registry_modules, missing_modules

        except Exception as e:
            print(f"❌ Error in registry check: {str(e)}")
            # Return all as missing on error, preserving full tuple structure
            missing_modules = []
            for feature in responsible_features:
                missing_modules.append(feature)  # Keep full tuple
            return "none_in_registry", [], missing_modules

    def load_keyword_mapping(self, migration_name: str, cloud_category: str) -> List[Dict[str, Any]]:
        """
        Load keyword-to-module mapping dynamically based on migration_name.

        Args:
            migration_name: Dynamic migration name from database configuration
            cloud_category: Cloud category from state (local/cloud)

        Returns:
            List of dictionaries containing Feature_Name, Keywords, Object_Path mapping
        """
        try:
            # Get QBook path dynamically based on cloud category from state
            if cloud_category.lower() == 'local':
                qbook_path = Config.Qbook_Local_Path
                print(f"🏠 Using Local QBook path: {qbook_path}")
            else:
                qbook_path = Config.Qbook_Path
                print(f"☁️ Using Cloud QBook path: {qbook_path}")

            # Dynamic CSV path: qbook_path/Conversion_Modules/{migration_name}/{migration_name}.csv
            csv_path = os.path.join(qbook_path, "Conversion_Modules", migration_name, f"{migration_name}.csv")

            if not os.path.exists(csv_path):
                print(f"⚠️ {migration_name}.csv not found at {csv_path}")
                return []

            df = pd.read_csv(csv_path)
            keyword_mapping = df[['Feature_Name', 'Keywords', 'Object_Path']].to_dict('records')

            print(f"📋 Loaded {len(keyword_mapping)} keyword mappings from {csv_path}")
            return keyword_mapping

        except Exception as e:
            print(f"❌ Failed to load keyword mapping: {str(e)}")
            return []

    def get_module_paths(self, available_features: List[tuple], migration_name: str, cloud_category: str) -> List[tuple]:
        """
        Get full paths to Python modules from available_features using dynamic paths.

        Args:
            available_features: List of tuples like [('update_alias', 'Common/Statement/Pre/update_alias.py')]
            migration_name: Dynamic migration name from database configuration
            cloud_category: Cloud category from state (local/cloud)

        Returns:
            List of tuples with (feature_name, full_module_path)
        """
        module_paths = []

        # Get QBook path dynamically based on cloud category from state
        if cloud_category.lower() == 'local':
            qbook_path = Config.Qbook_Local_Path
            print(f"🏠 Using Local QBook path for modules: {qbook_path}")
        else:
            qbook_path = Config.Qbook_Path
            print(f"☁️ Using Cloud QBook path for modules: {qbook_path}")

        for feature_name, relative_path in available_features:
            # Dynamic module path: qbook_path/Conversion_Modules/{migration_name}/{relative_path}
            full_path = os.path.join(qbook_path, "Conversion_Modules", migration_name, relative_path)
            module_paths.append((feature_name, full_path))

        print(f"📁 Resolved {len(module_paths)} module paths for analysis")
        return module_paths

    def decrypt_and_read_modules(self, module_paths: List[tuple], migration_name: str = None) -> Dict[str, str]:
        """
        Decrypt and read Python modules from given paths using existing decryption logic.

        Args:
            module_paths: List of tuples with (feature_name, module_path)
            migration_name: Migration name to get the correct encryption key

        Returns:
            Dictionary mapping feature_name to decrypted module content
        """
        decrypted_modules = {}

        # Get encryption key from database based on migration_name
        decrypt_key = get_encryption_key_for_migration(migration_name)
        if not decrypt_key:
            print(f"❌ No encryption key found for migration: {migration_name}")
            return {}

        for module_data in module_paths:
            try:
                # Handle different tuple formats safely
                if len(module_data) >= 2:
                    feature_name, module_path = module_data[0], module_data[1]
                else:
                    print(f"⚠️ Invalid module data format: {module_data}")
                    continue

                if os.path.exists(module_path):
                    # Use existing decryption function from object_conversion.py
                    decrypted_content = decrypt_conversion_file(module_path, decrypt_key)
                    decrypted_modules[feature_name] = decrypted_content
                    print(f"🔓 Decrypted module: {feature_name}")
                else:
                    print(f"⚠️ Module not found: {module_path}")

            except Exception as e:
                print(f"❌ Failed to decrypt module {feature_name}: {str(e)}")

        print(f"📚 Successfully decrypted {len(decrypted_modules)} modules")
        return decrypted_modules

    def ai_analyze_responsible_modules(self, statement_data: Dict[str, Any],
                                     decrypted_modules: Dict[str, str],
                                     keyword_mapping: List[Dict[str, Any]],
                                     migration_name: str,
                                     validation_feedback: Optional[str] = None,
                                     complete_available_features: List[tuple] = None) -> Dict[str, Any]:
        """
        Use AI to analyze which modules are responsible for conversion issues.

        Args:
            statement_data: Combined statement data with source, target, and AI converted statements
            decrypted_modules: Dictionary of decrypted Python module contents
            keyword_mapping: Keyword-to-module mapping from Oracle_Postgres14.csv
            migration_name: Migration name for database-specific terms
            validation_feedback: Optional feedback from previous validation attempts

        Returns:
            Dictionary containing responsible features analysis
        """
        try:
            # Extract key information for analysis
            # ai_converted_statement and original_deployment_error now contain the best available values
            # due to COALESCE logic applied in Stage1 database query
            original_source = statement_data.get('original_source_statement', '')
            ai_converted = statement_data.get('ai_converted_statement', '')  # Contains COALESCE(updated_statement, converted_statement)
            actual_target = statement_data.get('original_target_statement', '')
            deployment_error = statement_data.get('original_deployment_error', '')  # Contains COALESCE(new_deployment_error, error)
            # Use complete available features for AI analysis (available + post)
            available_features = complete_available_features if complete_available_features is not None else statement_data.get('available_features', [])

            # Get database-specific terms for prompts (clean names without versions)
            db_terms = get_database_specific_terms(migration_name)

            # Prepare AI analysis prompt using prompts folder with validation feedback
            analysis_prompt = create_responsible_features_identification_prompt(
                original_source, ai_converted, actual_target,
                deployment_error, decrypted_modules, keyword_mapping, available_features,
                validation_feedback=validation_feedback,
                db_terms=db_terms
            )

            # Use structured output for reliable AI analysis
            structured_llm = self.llm.client.with_structured_output(ResponsibleFeaturesAnalysisOutput)
            ai_result = structured_llm.invoke(analysis_prompt)

            # Extract responsible features and analysis summary with complete module paths
            responsible_features = []
            for feature in ai_result.responsible_features:
                original_feature_name = feature.feature_name
                module_path = feature.module_path

                # Fix feature name to match actual Python function name (lowercase)
                # CSV might have "Xml_sequence" but function is "xml_sequence"
                corrected_feature_name = original_feature_name.lower()

                # Complete the module path if it's incomplete (from CSV keyword mapping)
                if module_path.endswith('.py'):
                    # Path is already complete (e.g., from available features)
                    complete_path = module_path
                else:
                    # Path is incomplete (e.g., from CSV keyword mapping like "Common/Pre")
                    # Add the lowercase feature name + .py (using corrected name)
                    complete_path = f"{module_path}/{corrected_feature_name}.py"

                # Get both AI responsibility reason and CSV keywords
                # AI responsibility reason: for responsibility tracking and context
                # CSV keywords: for enhancement prompts and module analysis
                ai_responsibility_reason = feature.responsibility_reason

                # Find CSV keywords for this feature from keyword mapping
                feature_keywords = ''
                for mapping in keyword_mapping:
                    if mapping.get('Feature_Name', '').lower() == corrected_feature_name.lower():
                        feature_keywords = mapping.get('Keywords', '')
                        break

                # Store feature with both AI responsibility reason and CSV keywords as tuple:
                # (feature_name, module_path, responsibility_reason, keywords)
                responsible_features.append((corrected_feature_name, complete_path, ai_responsibility_reason, feature_keywords))

                if original_feature_name != corrected_feature_name:
                    print(f"🔧 Corrected feature name: {original_feature_name} → {corrected_feature_name}")
                print(f"🔧 Completed path for {corrected_feature_name}: {complete_path}")
                if ai_responsibility_reason:
                    print(f"🎯 AI Responsibility for {corrected_feature_name}: {ai_responsibility_reason}")
                if feature_keywords:
                    print(f"🔑 CSV Keywords for {corrected_feature_name}: {feature_keywords}")

            analysis_summary = ai_result.analysis_summary

            print(f"🧠 AI identified {len(responsible_features)} responsible modules")

            if responsible_features:
                print("🎯 Responsible modules identified by AI:")
                for feature in ai_result.responsible_features:
                    print(f"   - {feature.feature_name}: {feature.responsibility_reason}")
            else:
                print("⚠️ No responsible modules identified - statement will be skipped")

            return {
                "responsible_features": responsible_features,
                "analysis_summary": analysis_summary
            }

        except Exception as e:
            print(f"❌ AI analysis failed: {str(e)}")
            return {
                "responsible_features": [],
                "analysis_summary": f"AI analysis failed: {str(e)}. Manual review required."
            }


    def extract_module_names(self, module_tuples: List[tuple]) -> List[str]:
        """Extract module names from tuple list"""
        names = []
        for module_tuple in module_tuples:
            if len(module_tuple) > 0:
                names.append(module_tuple[0])
        return names

    
    def ai_enhance_with_sequential_context(self, combined_code: str, enhancement_context: Dict[str, Any]) -> str:
        """Use AI to enhance combined module with sequential execution context and responsibility reasoning"""
        try:
            prompt = create_sequential_enhancement_prompt(combined_code, enhancement_context)

            structured_llm = self.llm.client.with_structured_output(ModuleEnhancementOutput)
            ai_result = structured_llm.invoke(prompt)

            print(f"🤖 AI enhancement completed with sequential context")
            print(f"   🔧 AI reported code_changed: {ai_result.code_changed}")
            print(f"   📝 AI analysis: {ai_result.analysis}")
            print(f"   📊 Original code length: {len(combined_code)} chars")
            print(f"   📊 Enhanced code length: {len(ai_result.enhanced_code)} chars")

            # Check if AI actually made changes
            if combined_code.strip() == ai_result.enhanced_code.strip():
                print(f"   ⚠️ WARNING: AI returned identical code (no actual changes)")
            else:
                print(f"   ✅ AI made actual changes to the code")

            return ai_result.enhanced_code

        except Exception as e:
            print(f"❌ AI enhancement error: {str(e)}")
            return combined_code


    def get_current_source_statement_number(self, state: Stage2WorkflowState) -> int:
        """
        Get the source statement number for the current statement being processed.

        Args:
            state: Stage2WorkflowState containing current statement data

        Returns:
            Source statement number from the original source
        """
        current_statement_index = getattr(state, 'current_statement_index', 0)
        available_features_with_statements = getattr(state, 'available_features_with_statements', [])

        if not available_features_with_statements:
            raise ValueError(f"❌ ERROR: available_features_with_statements is empty for statement index {current_statement_index}")

        if current_statement_index >= len(available_features_with_statements):
            raise ValueError(f"❌ ERROR: current_statement_index {current_statement_index} >= available statements {len(available_features_with_statements)}")

        current_statement = available_features_with_statements[current_statement_index]
        source_statement_number = current_statement.get('source_statement_number')

        if source_statement_number is None:
            raise ValueError(f"❌ ERROR: source_statement_number missing for statement index {current_statement_index}")

        return source_statement_number

    def setup_module_update_paths(self, state: Stage2WorkflowState, source_statement_number: int, attempt_number: int) -> Dict[str, str]:
        """
        Setup paths for Stage1_Metadata directory structure with statement-specific directories and attempt tracking.

        Args:
            state: Stage2WorkflowState containing migration details
            source_statement_number: Source statement number (from original source) for directory organization
            attempt_number: Current attempt number for tracking

        Returns:
            Dictionary containing path information
        """
        try:
            # Get cloud category from state
            cloud_category = getattr(state, 'cloud_category', 'cloud')

            # Get Stage1_Metadata path based on cloud category
            if cloud_category.lower() == 'local':
                stage1_metadata_path = Config.Qbook_Local_Path
            else:
                stage1_metadata_path = Config.Qbook_Path

            # Get dynamic folder name based on process type
            feature_modules_folder = self.get_feature_modules_folder_name(state.process_type)

            # Create directory structure: Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/{process_type}_feature_modules/{statement_number}
            feature_modules_base_dir = os.path.join(
                stage1_metadata_path,
                "Stage1_Metadata",
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name,
                feature_modules_folder
            )

            # Statement-specific directory using source statement number
            feature_modules_dir = os.path.join(feature_modules_base_dir, str(source_statement_number))

            # Create directory if it doesn't exist
            os.makedirs(feature_modules_dir, exist_ok=True)

            print(f"📁 Feature modules directory ({feature_modules_folder}): {feature_modules_dir}")
            print(f"📊 Processing source statement: {source_statement_number}, attempt: {attempt_number}")

            return {
                "feature_modules_dir": feature_modules_dir,
                "stage1_metadata_path": stage1_metadata_path
            }

        except Exception as e:
            print(f"❌ Error setting up module update paths: {str(e)}")
            raise

    def read_and_decrypt_module(self, feature_name: str, module_path: str, migration_name: str, cloud_category: str, purpose: str = "processing") -> str:
        """
        Read and decrypt Python module from QMigrator conversion modules.

        Args:
            feature_name: Name of the feature
            module_path: Path to the module
            migration_name: Migration name
            cloud_category: Cloud category for path selection

        Returns:
            Decrypted module content as string
        """
        try:
            # Get base path based on cloud category
            if cloud_category.lower() == 'local':
                base_path = Config.Qbook_Local_Path
            else:
                base_path = Config.Qbook_Path

            # Construct full module path
            # module_path already contains the full path including .py file
            full_module_path = os.path.join(
                base_path,
                'Conversion_Modules',
                migration_name,
                module_path
            )

            print(f"📖 Reading module from: {full_module_path} (Purpose: {purpose})")

            if not os.path.exists(full_module_path):
                print(f"⚠️ Module file not found: {full_module_path}")
                return ""

            # Decrypt the module using the same method as other nodes
            # Get encryption key from database based on migration_name
            decrypt_key = get_encryption_key_for_migration(migration_name)
            if not decrypt_key:
                print(f"❌ No encryption key found for migration: {migration_name}")
                return ""

            decrypted_content = decrypt_conversion_file(full_module_path, decrypt_key)

            print(f"✅ Successfully read and decrypted module: {feature_name} (Purpose: {purpose})")
            return decrypted_content

        except Exception as e:
            print(f"❌ Error reading module {feature_name}: {str(e)}")
            return ""

    
    def save_attempt_to_memory(self, state: Stage2WorkflowState, attempt_number: int, updated_modules: List[Dict[str, Any]], ai_comparison_feedback: str = None, final_output: str = None) -> Dict[str, Any]:
        """
        Save the current attempt to in-memory storage for future learning.
        Returns state update instead of mutating directly.

        Args:
            state: Current workflow state
            attempt_number: Current attempt number
            updated_modules: List of modules used in this attempt
            ai_comparison_feedback: Feedback from AI comparison explaining why it failed
            final_output: The final output produced by this attempt

        Returns:
            Dict containing attempt_history update
        """
        try:
            # Get current attempt history from state
            attempt_history = getattr(state, 'attempt_history', [])

            # Check if this attempt number already exists
            existing_attempt = any(
                attempt.get('attempt_number') == attempt_number
                for attempt in attempt_history
            )

            if existing_attempt:
                print(f"⚠️ Attempt {attempt_number} already exists in memory - skipping duplicate")
                return {}

            # Create attempt record
            attempt_record = {
                'attempt_number': attempt_number,
                'modules_used': [],
                'ai_feedback': ai_comparison_feedback or f"Attempt {attempt_number} failed - modules combination did not produce expected output",
                'final_output': final_output,
                'status': 'FAILED',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # Add module information
            for module in updated_modules:
                module_info = {
                    'module_name': module.get('module_name', 'unknown'),
                    'module_code': module.get('code_content', module.get('updated_code', '')),
                    'file_path': module.get('file_path', module.get('updated_module_path', ''))
                }
                attempt_record['modules_used'].append(module_info)

            # Append to attempt history
            attempt_history.append(attempt_record)

            print(f"💾 Saved attempt {attempt_number} to memory ({len(updated_modules)} modules)")
            print(f"📊 Total attempts in memory: {len(attempt_history)}")

            # Log attempt to Excel for tracking
            if hasattr(state, 'stage2_excel_path') and state.stage2_excel_path:
                try:
                    current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                    current_statement_index = getattr(state, 'current_statement_index', 0)

                    # Extract detailed module information
                    module_names = []
                    module_types = []
                    module_details = []

                    for mod in updated_modules:
                        module_name = mod.get('module_name', 'unknown_module')
                        module_type = mod.get('module_type', 'unknown_type')
                        module_names.append(module_name)
                        module_types.append(module_type)

                        # Create detailed module info
                        changes_made = mod.get('changes_made', mod.get('has_functional_changes', 'unknown'))
                        file_path = mod.get('file_path', 'no_path')
                        module_details.append(f"{module_name} ({module_type}) - Changes: {changes_made} - Path: {file_path}")

                    # Get source statement number using dedicated function
                    source_statement_number = self.get_current_source_statement_number(state)

                    excel_data = [{
                        "Statement_ID": current_statement_index + 1,
                        "Source_Statement_Number": source_statement_number,
                        "Attempt_Number": attempt_number,
                        "Modules_Count": len(updated_modules),
                        "Module_Names": module_names,
                        "Module_Types": module_types,
                        "Module_Details": module_details,
                        "Modules_Used": [mod.get('module_name', 'unknown') for mod in updated_modules],  # Keep for backward compatibility
                        "AI_Feedback": ai_comparison_feedback or "No feedback available",
                        "Final_Output_Length": len(final_output) if final_output else 0,
                        "Status": "FAILED",
                        "Timestamp": current_timestamp
                    }]

                    append_sheet_to_excel(state.stage2_excel_path, "Attempt_History", excel_data)
                    print(f"📊 Logged attempt {attempt_number} to Excel Attempt_History sheet")

                except Exception as e:
                    print(f"❌ Error logging attempt history to Excel: {str(e)}")

            # Return state update instead of mutating directly
            return {"attempt_history": attempt_history}

        except Exception as e:
            print(f"⚠️ Error saving attempt to memory: {str(e)}")
            return {}

    
    def ai_compare_statement_functionality(self, statement_data: Dict[str, Any],
                                         migration_name: str, responsible_modules_context: list = None) -> Dict[str, Any]:
        """
        Use AI to compare functional equivalence between AI corrected and applied modules statements.
        Follows the same pattern as other AI analysis functions in the codebase.

        Args:
            statement_data: Dictionary containing all statement information
            migration_name: Migration name for database-specific terms

        Returns:
            Dict containing detailed comparison results
        """
        try:
            # Extract only the two statements that need to be compared
            ai_corrected_statement = statement_data.get('ai_converted_statement', '')
            applied_modules_statement = statement_data.get('updated_ai_converted_statement', '')

            # Get database-specific terms for prompts (clean names without versions)
            db_terms = get_database_specific_terms(migration_name)

            # COMPARISON PURPOSE ONLY: Remove comments for clean AI comparison
            ai_corrected_for_comparison = remove_comments_for_comparison(ai_corrected_statement)
            applied_modules_for_comparison = remove_comments_for_comparison(applied_modules_statement)

            # Create AI analysis prompt using clean statements for comparison only
            analysis_prompt = create_ai_statement_comparison_prompt(
                ai_corrected_statement=ai_corrected_for_comparison,
                applied_modules_statement=applied_modules_for_comparison,
                db_terms=db_terms,
                responsible_modules_context=responsible_modules_context
            )

            # Use structured output for reliable AI analysis (same pattern as other nodes)
            structured_llm = self.llm.client.with_structured_output(StatementComparisonOutput)
            ai_result = structured_llm.invoke(analysis_prompt)

            print(f"🧠 AI comparison result: {'MATCH' if ai_result.statements_match else 'NO MATCH'}")
            if not ai_result.statements_match:
                print(f"📝 Feedback: {ai_result.explanation}")

                # Show module-specific transformation guidance
                transformation_guidance = ai_result.transformation_guidance
                if transformation_guidance and transformation_guidance.required:
                    print(f"🛠️ MODULE-SPECIFIC TRANSFORMATION GUIDANCE:")
                    specific_changes = transformation_guidance.specific_changes
                    for i, change in enumerate(specific_changes, 1):
                        print(f"   {i}. Source Pattern: {change.source_pattern}")
                        print(f"      Target Pattern: {change.target_pattern}")
                        print(f"      Module Guidance: {change.transformation_method}")
                        print(f"      Variations: {change.variations_to_handle}")

                    implementation_steps = transformation_guidance.implementation_steps
                    if implementation_steps:
                        print(f"📋 Implementation Steps:")
                        for i, step in enumerate(implementation_steps, 1):
                            print(f"   {i}. {step}")

            return {
                "statements_match": ai_result.statements_match,
                "explanation": ai_result.explanation,
                "transformation_guidance": ai_result.transformation_guidance.dict() if ai_result.transformation_guidance else {}
            }

        except Exception as e:
            print(f"❌ AI comparison failed: {str(e)}")
            return {
                "statements_match": False,
                "explanation": f"AI comparison failed: {str(e)}. Manual review required."
            }

    def more_statements_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 12: More Statements Decision Node.

        Purpose: Determine if more statements need processing or if current statement needs retry.

        This function implements the statement-by-statement processing logic:
        - Process one statement through entire workflow
        - If statement succeeds: move to next statement
        - If statement fails and attempts < max: retry current statement
        - If statement fails and attempts = max: move to next statement
        - If no more statements: complete workflow

        Returns:
            Dict with workflow control flags
        """
        print("🔄 Starting More Statements Decision...")

        try:
            # Get current state information from new pipeline
            iteration_action = getattr(state, 'iteration_action', None)
            statements_match = getattr(state, 'statements_match', False)
            current_attempt = getattr(state, 'current_attempt', 1)
            max_attempts = getattr(state, 'max_attempts', 5)
            current_statement_index = getattr(state, 'current_statement_index', 0)

            # Get current statement data and total count
            combined_data = getattr(state, 'available_features_with_statements', [])
            total_statements = len(combined_data)
            current_statement_data = combined_data[current_statement_index] if combined_data and current_statement_index < len(combined_data) else {}

            # Get source statement number for better logging
            current_source_stmt_num = current_statement_data.get('source_statement_number', current_statement_index + 1)

            print(f"📊 Current Status:")
            print(f"   📝 Source Statement: {current_source_stmt_num} (position {current_statement_index + 1}/{total_statements})")
            print(f"   🔄 Attempt: {current_attempt}/{max_attempts}")
            print(f"   ✅ Statements Match: {statements_match}")
            print(f"   🎯 Iteration Action: {iteration_action}")

            # Excel logging for statement decision tracking
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Decision logic based on new pipeline iteration control
            if iteration_action == "proceed" or statements_match:
                # Current statement succeeded, check if more statements exist
                if current_statement_index < total_statements - 1:
                    next_index = current_statement_index + 1
                    print(f"✅ Source statement {current_source_stmt_num} completed successfully")
                    print(f"📝 Moving to next statement (position {next_index + 1}/{total_statements})")

                    # Clear attempt history for completed statement is handled in return data

                    # Log successful statement completion
                    if excel_path:
                        excel_data = [{
                            "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                            "Attempt_Number": current_attempt,
                            "Decision": "MOVE_TO_NEXT",
                            "Status": "SUCCESS",
                            "Analysis": f"Statement completed successfully after {current_attempt} attempt(s)",
                            "Timestamp": current_timestamp
                        }]
                        append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                        print(f"📋 Logged successful completion of source statement {current_source_stmt_num}")

                    return {
                        "current_statement_index": next_index,
                        "current_attempt": 1,  # Reset attempts for new statement
                        "validation_attempt": 1,  # Reset validation attempts for new statement
                        "execution_attempt": 1,  # Reset execution attempts for new statement
                        "validation_feedback": None,  # Clear validation feedback for new statement
                        "ai_comparison_feedback": None,  # Clear AI comparison feedback for new statement
                        "execution_feedback": None,  # Clear execution feedback for new statement
                        "current_statement": None,  # Clear current statement data
                        "responsible_features": None,  # Clear responsible features
                        "responsible_procedures": None,  # Clear responsible procedures
                        "updated_modules": None,  # Clear updated modules
                        "attempt_history": [],  # Clear attempt history for new statement

                        # Clear new pipeline state fields for new statement
                        "module_categories": None,
                        "pre_processed_output": None,
                        "driver_module_code": None,
                        "driver_module_path": None,
                        "enhanced_driver_code": None,
                        "enhanced_driver_path": None,
                        "decomposed_modules": None,
                        "validation_results": None,
                        "validation_passed": False,
                        "execution_success": False,
                        "final_output": None,
                        "statements_match": False,
                        "iteration_action": None,

                        "updated_ai_converted_statement": None,  # Clear updated statement
                        "current_statement_id": None,  # Clear statement ID

                        # Clear registry integration fields for new statement
                        "registry_status": None,  # Clear registry status
                        "registry_modules": None,  # Clear registry modules
                        "missing_modules": None,  # Clear missing modules
                        "enhanced_modules_loaded": False,  # Reset enhanced modules flag
                        "module_source": "original",  # Reset to default module source
                        "execution_source": "enhancement",  # Reset to default execution source
                        "registry_updated": False,  # Reset registry update flag
                        "saved_modules": None,  # Clear saved modules list
                        "skipped_modules": None,  # Clear skipped modules list
                        "total_modules_processed": 0,  # Reset modules processed counter

                        # Clear comments handling for new statement
                        "comments_dict": None,  # Clear statement-specific comments

                        "ai_statements_match": False,  # Reset comparison flag
                        "workflow_action": "next_statement"
                    }
                else:
                    print(f"🎉 All {total_statements} statements processed successfully!")

                    # Log workflow completion
                    if excel_path:
                        excel_data = [{
                            "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                            "Attempt_Number": current_attempt,
                            "Decision": "WORKFLOW_COMPLETE",
                            "Status": "SUCCESS",
                            "Analysis": f"All {total_statements} statements processed successfully",
                            "Timestamp": current_timestamp
                        }]
                        append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                        print(f"📋 Logged workflow completion")

                    return {
                        "workflow_action": "complete",
                        "workflow_completed": True
                    }
            elif iteration_action == "retry" or (not statements_match and current_attempt < max_attempts):
                # Current statement failed, retry enhancement with feedback
                print(f"❌ Source statement {current_source_stmt_num} failed - retrying enhancement with feedback")
                print(f"🔢 Attempt tracking: current={current_attempt}, max={max_attempts}")

                # Log retry decision
                if excel_path:
                    excel_data = [{
                        "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                        "Attempt_Number": current_attempt,
                        "Decision": "RETRY_ENHANCEMENT",
                        "Status": "RETRY",
                        "Analysis": f"Enhancement failed, retrying with feedback (attempt {current_attempt}/{max_attempts})",
                        "Timestamp": current_timestamp
                    }]
                    append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                    print(f"📋 Logged retry decision for source statement {current_source_stmt_num}")

                return {
                    "workflow_action": "retry_current"
                }
            elif iteration_action == "fail" or (not statements_match and current_attempt >= max_attempts):
                # Max attempts reached or explicit failure, move to next statement
                print(f"🛑 Source statement {current_source_stmt_num} failed after {current_attempt} attempts")
                if current_statement_index < total_statements - 1:
                    next_index = current_statement_index + 1
                    print(f"📝 Moving to next statement (position {next_index + 1}/{total_statements})")

                    # Clear attempt history for failed statement is handled in return data

                    # Log max attempts reached, moving to next
                    if excel_path:
                        excel_data = [{
                            "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                            "Attempt_Number": current_attempt,
                            "Decision": "MOVE_TO_NEXT_MAX_ATTEMPTS",
                            "Status": "FAILED",
                            "Analysis": f"Statement failed after {max_attempts} attempts, moving to next statement",
                            "Timestamp": current_timestamp
                        }]
                        append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                        print(f"📋 Logged max attempts reached for source statement {current_source_stmt_num}")

                    return {
                        "current_statement_index": next_index,
                        "current_attempt": 1,  # Reset attempts for new statement
                        "validation_attempt": 1,  # Reset validation attempts for new statement
                        "execution_attempt": 1,  # Reset execution attempts for new statement
                        "validation_feedback": None,  # Clear validation feedback for new statement
                        "ai_comparison_feedback": None,  # Clear AI comparison feedback for new statement
                        "execution_feedback": None,  # Clear execution feedback for new statement
                        "current_statement": None,  # Clear current statement data
                        "responsible_features": None,  # Clear responsible features
                        "responsible_procedures": None,  # Clear responsible procedures
                        "updated_modules": None,  # Clear updated modules
                        "attempt_history": [],  # Clear attempt history for new statement

                        # Clear new pipeline state fields for new statement
                        "module_categories": None,
                        "pre_processed_output": None,
                        "driver_module_code": None,
                        "driver_module_path": None,
                        "enhanced_driver_code": None,
                        "enhanced_driver_path": None,
                        "decomposed_modules": None,
                        "validation_results": None,
                        "validation_passed": False,
                        "execution_success": False,
                        "final_output": None,
                        "statements_match": False,
                        "iteration_action": None,

                        "updated_ai_converted_statement": None,  # Clear updated statement
                        "current_statement_id": None,  # Clear statement ID

                        # Clear registry integration fields for new statement
                        "registry_status": None,  # Clear registry status
                        "registry_modules": None,  # Clear registry modules
                        "missing_modules": None,  # Clear missing modules
                        "enhanced_modules_loaded": False,  # Reset enhanced modules flag
                        "module_source": "original",  # Reset to default module source
                        "execution_source": "enhancement",  # Reset to default execution source
                        "registry_updated": False,  # Reset registry update flag
                        "saved_modules": None,  # Clear saved modules list
                        "skipped_modules": None,  # Clear skipped modules list
                        "total_modules_processed": 0,  # Reset modules processed counter

                        # Clear comments handling for new statement
                        "comments_dict": None,  # Clear statement-specific comments

                        "ai_statements_match": False,  # Reset comparison flag
                        "workflow_action": "next_statement"
                    }
                else:
                    print(f"🎉 All {total_statements} statements processed (some with max attempts)")

                    # Log final workflow completion with some failures
                    if excel_path:
                        excel_data = [{
                            "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                            "Attempt_Number": current_attempt,
                            "Decision": "WORKFLOW_COMPLETE_WITH_FAILURES",
                            "Status": "COMPLETED",
                            "Analysis": f"All {total_statements} statements processed, some reached max attempts",
                            "Timestamp": current_timestamp
                        }]
                        append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                        print(f"📋 Logged final workflow completion")

                    return {
                        "workflow_action": "complete",
                        "workflow_completed": True
                    }
            else:
                # Unexpected state - default to completion
                print(f"⚠️ Unexpected state in more_statements_decision - defaulting to completion")
                print(f"   📊 iteration_action: {iteration_action}")
                print(f"   ✅ statements_match: {statements_match}")
                print(f"   🔄 current_attempt: {current_attempt}/{max_attempts}")

                return {
                    "workflow_action": "complete",
                    "workflow_completed": True
                }

        except Exception as e:
            print(f"❌ More Statements Decision failed: {str(e)}")
            return {
                "workflow_action": "complete",
                "error": str(e)
            }

    
    def apply_feature_module(self, statement: str, feature_name: str, module_code: str, state: Stage2WorkflowState) -> str:
        """
        Execute a feature module to transform the statement using importlib.util.
        """
        if not module_code.strip():
            raise Exception(f"Empty module code for {feature_name}")

        try:
            print('Checking module code for the feature: ' + feature_name)
            spec = importlib.util.spec_from_loader(feature_name, loader=None, origin="<in-memory>")
            module = importlib.util.module_from_spec(spec)

            # Execute the module code
            exec(module_code, module.__dict__)

        except Exception as exec_error:
            print(f"⚠️ Module execution error for {feature_name}: {str(exec_error)}")
            # Instead of silently returning original statement, raise the error
            # This will be caught by execute_complete_pipeline and trigger enhancement retry
            raise RuntimeError(f"Module execution error in {feature_name}: {str(exec_error)}")

        # Try to call the function if it exists
        if hasattr(module, feature_name) and callable(getattr(module, feature_name)):
            print(f"Calling function {feature_name}")
            try:
                schema_name = getattr(state, 'schema_name', None)
                if not schema_name:
                    print(f"⚠️ No schema_name found in state for {feature_name}")
                    return statement

                result = getattr(module, feature_name)(statement, schema_name)
                print(f"🔧 Module {feature_name} execution result: input_length={len(statement)}, output_length={len(str(result))}, same_content={result == statement}")
                return str(result)
            except Exception as func_error:
                print(f"⚠️ Function call error for {feature_name}: {str(func_error)}")
                # Instead of silently returning original statement, raise the error
                # This will be caught by execute_complete_pipeline and trigger enhancement retry
                raise RuntimeError(f"Runtime error in {feature_name}: {str(func_error)}")
        else:
            print(f"⚠️ Function {feature_name} not found in module")
            transformed_statement = (
                getattr(module, 'output_statement', None) or
                getattr(module, 'pre_output', None) or
                getattr(module, 'source_data', None) or
                statement
            )
            return str(transformed_statement)



    def complete_processing(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 13: Complete Processing Node.

        Purpose: Finalize Stage 2 workflow and prepare outputs.

        Returns:
            Dict containing final workflow results
        """
        print("🔄 Starting Complete Processing...")

        try:
            # Copy Stage 2 Excel file from temp to QBook path
            if state.stage2_excel_path and state.final_qbook_excel_path:
                if os.path.exists(state.stage2_excel_path):
                    qbook_dir = os.path.dirname(state.final_qbook_excel_path)
                    os.makedirs(qbook_dir, exist_ok=True)
                    shutil.copy2(state.stage2_excel_path, state.final_qbook_excel_path)

                    if os.path.exists(state.final_qbook_excel_path):
                        print("✅ Excel file copied to QBook path successfully")
                    else:
                        print("❌ Excel file copy failed")

            print("✅ Stage 2 workflow completed!")

            return {
                "workflow_completed": True,
                "final_excel_path": state.final_qbook_excel_path if state.final_qbook_excel_path else ""
            }

        except Exception as e:
            error_msg = f"❌ Complete Processing failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "workflow_completed": False,
                "final_excel_path": ""
            }
    
    # ==================== UTILITY FUNCTIONS ====================
    
    def cleanup_process_feature_modules(self, state: Stage2WorkflowState) -> None:
        """
        Clean process-type-specific feature modules directory for fresh start when reprocessing.

        Deletes based on process type:
        - QMigrator: qbookv2/Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/qm_feature_modules
        - QBook: qbookv2/Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/qbook_feature_modules

        Args:
            state: Stage2WorkflowState containing object details and process type
        """
        try:
            # Determine paths based on cloud_category
            if state.cloud_category.lower() == "local":
                stage1_metadata_path = Config.Qbook_Local_Path
            else:
                stage1_metadata_path = Config.Qbook_Path

            # Get process-type-specific folder name
            feature_modules_folder = self.get_feature_modules_folder_name(state.process_type)

            # Build process-specific feature modules directory path
            feature_modules_dir = os.path.join(
                stage1_metadata_path,
                "Stage1_Metadata",
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name,
                feature_modules_folder
            )

            if os.path.exists(feature_modules_dir):
                print(f"🗑️ Cleaning {state.process_type} feature modules directory: {feature_modules_dir}")
                shutil.rmtree(feature_modules_dir)
                print(f"🗑️ Successfully deleted {feature_modules_folder} directory")
            else:
                print(f"📁 {feature_modules_folder} directory doesn't exist (first run): {feature_modules_dir}")

        except Exception as e:
            print(f"⚠️ Warning: Could not clean {state.process_type} feature modules directory: {str(e)}")

    def get_feature_modules_folder_name(self, process_type: str) -> str:
        """
        Get dynamic folder name based on process type.

        Args:
            process_type: "qmigrator" or "qbook"

        Returns:
            "qm_feature_modules" for qmigrator, "qbook_feature_modules" for qbook
        """
        if process_type == "qmigrator":
            return "qm_feature_modules"
        else:
            return "qbook_feature_modules"

    # ==================== NEW PIPELINE NODES ====================
    def categorize_execution_modules(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 6: Categorize Execution Modules - Pipeline Preparation

        Purpose:
            Organizes all available conversion modules into three execution categories:
            pre-execution, responsible, and post-execution. This categorization enables
            the sequential pipeline approach where modules are executed in proper order.

        Business Logic:
            1. Pre-execution: Features that appear before the first responsible feature
            2. Responsible: Features identified by AI analysis as needing enhancement
            3. Post-execution: Features from post_features column (cleanup, formatting)

        Categorization Rules:
            - Uses Excel post_features column as authoritative source for post-processing
            - Identifies first responsible feature position in available_features list
            - Everything before first responsible = pre-execution
            - Responsible features = those identified by AI analysis
            - Post features = explicitly defined post-processing modules

        Input Requirements:
            - state.available_features_with_statements: Combined statement and feature data
            - state.responsible_features: AI-identified responsible features
            - state.current_statement_index: Current statement being processed

        Output:
            - module_categories: Dict with pre_execution, responsible, post_execution lists
            - categorization_summary: Summary of module counts per category
            - execution_order: Planned execution sequence

        Next Nodes:
            - Success → execute_pre_features
            - No modules → complete_processing

        Error Handling:
            - Missing statement data validation
            - Empty feature lists handling
            - Categorization logic errors
        """
        print("\n" + "="*80)
        print("📋 CATEGORIZE EXECUTION MODULES")
        print("="*80)

        try:
            # Get current statement data
            current_statement_index = getattr(state, 'current_statement_index', 0)
            available_features_with_statements = getattr(state, 'available_features_with_statements', [])

            if not available_features_with_statements or current_statement_index >= len(available_features_with_statements):
                return {"success": False, "error": "No current statement data available"}

            current_statement = available_features_with_statements[current_statement_index]

            # Get complete pipeline features for dynamic categorization
            available_features = current_statement.get('available_features', [])
            post_features = current_statement.get('post_features', [])

            # Get responsible features from AI analysis
            responsible_features = getattr(state, 'responsible_features', [])

            print(f"📋 Input for categorization:")
            print(f"   📋 Available features: {len(available_features)} modules")
            print(f"   📤 Post features: {len(post_features)} modules")
            print(f"   🎯 Responsible features: {len(responsible_features)} modules")

            # Categorize modules using dynamic logic
            module_categories = self.categorize_modules_by_execution_order(
                available_features, responsible_features, post_features
            )

            # Log to Excel
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                # Get source statement number using dedicated function
                source_statement_number = self.get_current_source_statement_number(state)

                self.log_pipeline_preparation_to_excel(excel_path, {
                    'operation': 'MODULE_CATEGORIZATION',
                    'statement_number': current_statement_index + 1,
                    'source_statement_number': source_statement_number,
                    'attempt_number': getattr(state, 'current_attempt', 1),
                    'module_categories': module_categories,
                    'categorization_summary': f"Pre: {len(module_categories['pre_execution'])}, Responsible: {len(module_categories['responsible'])}, Post: {len(module_categories['post_execution'])}"
                })

            print(f"✅ Module categorization completed:")
            print(f"   📋 Pre-execution: {len(module_categories['pre_execution'])} modules")
            print(f"   🎯 Responsible: {len(module_categories['responsible'])} modules")
            print(f"   📤 Post-execution: {len(module_categories['post_execution'])} modules")

            return {
                "module_categories": module_categories,
                "success": True
            }

        except Exception as e:
            print(f"❌ Error in categorize_execution_modules: {str(e)}")
            return {"success": False, "error": str(e)}

    def execute_pre_features(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 7: Execute Pre-Features.

        Purpose: Execute pre-processing modules sequentially to prepare input
        for responsible module enhancement.
        """
        print("\n" + "="*80)
        print("🔄 EXECUTE PRE-FEATURES")
        print("="*80)

        try:
            # Get module categories and current statement
            module_categories = getattr(state, 'module_categories', {})
            pre_execution_modules = module_categories.get('pre_execution', [])

            current_statement_index = getattr(state, 'current_statement_index', 0)
            available_features_with_statements = getattr(state, 'available_features_with_statements', [])
            current_statement = available_features_with_statements[current_statement_index]

            # Get original statement after type casting
            original_statement = current_statement.get('statement_after_typecasting',
                                                     current_statement.get('original_source_statement', ''))

            # Execute pre-modules sequentially
            current_output = original_statement
            execution_log = []

            print(f"🔍 Starting pre-feature execution with {len(pre_execution_modules)} modules")
            print(f"📝 Initial input: {original_statement}")

            for module_name, module_path in pre_execution_modules:
                try:
                    previous_output = current_output
                    module_type = 'unknown'

                    # Registry-first logic for pre-modules (same as responsible/post)
                    if check_registry_for_module(module_path, state.migration_name, state.cloud_category):
                        print(f"🔍 Executing enhanced pre-module from registry: {module_name}")
                        enhanced_code = load_enhanced_module_from_registry(module_path, state.migration_name, state.cloud_category)
                        if enhanced_code:
                            current_output = self.execute_enhanced_module(current_output, module_name, enhanced_code, state.schema_name)
                            module_type = 'enhanced_registry'
                        else:
                            raise RuntimeError(f"Failed to load enhanced module from registry: {module_name}")
                    else:
                        print(f"🔍 Executing original pre-module: {module_name}")
                        original_code = self.read_and_decrypt_module(module_name, module_path,
                                                                   state.migration_name, state.cloud_category, "pre_execution")
                        if original_code:
                            current_output = self.apply_feature_module(current_output, module_name, original_code, state)
                            module_type = 'original'
                        else:
                            raise RuntimeError(f"Failed to load original module: {module_name}")

                    # Log transformation
                    transformation_applied = current_output != previous_output
                    execution_log.append({
                        'module_name': module_name,
                        'module_path': module_path,
                        'module_type': module_type,
                        'transformation_applied': transformation_applied,
                        'input_length': len(previous_output),
                        'output_length': len(current_output)
                    })

                    print(f"   ✅ {module_name} ({module_type}): {'Applied transformation' if transformation_applied else 'No changes'}")

                except Exception as e:
                    print(f"   ❌ {module_name}: Execution error - {str(e)}")
                    # Continue with next module even if one fails

            # Log to Excel using new hierarchical system
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                # Get module counts from state for consistent data
                module_categories = getattr(state, 'module_categories', {})
                pre_count = len(module_categories.get('pre_execution', []))
                responsible_count = len(module_categories.get('responsible', []))
                post_count = len(module_categories.get('post_execution', []))
                total_modules = pre_count + responsible_count + post_count

                # Get source statement number using dedicated function
                source_statement_number = self.get_current_source_statement_number(state)

                # Log pipeline overview update with correct data
                self.log_pipeline_overview(excel_path, {
                    'statement_number': current_statement_index + 1,
                    'source_statement_number': source_statement_number,
                    'attempt_number': getattr(state, 'current_attempt', 1),
                    'total_modules': total_modules,
                    'pre_count': pre_count,
                    'responsible_count': responsible_count,
                    'post_count': post_count,
                    'overall_status': 'PRE_EXECUTION_COMPLETE',
                    'pipeline_phase': 'Pre-execution',
                    'success': True,
                    'notes': f"Executed {len(pre_execution_modules)} pre-execution modules. Total pipeline: {pre_count} pre + {responsible_count} responsible + {post_count} post = {total_modules} modules"
                })

                # Log each pre-execution module
                for i, log_entry in enumerate(execution_log):
                    self.log_module_processing(excel_path, {
                        'statement_number': current_statement_index + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'operation': 'PRE_EXECUTION',
                        'module_name': log_entry['module_name'],
                        'module_type': 'pre',
                        'input_code': f"Input length: {log_entry['input_length']} chars",
                        'output_code': f"Output length: {log_entry['output_length']} chars",
                        'status': 'EXECUTED',
                        'changes_made': log_entry['transformation_applied'],
                        'file_path': log_entry['module_path'],
                        'module_order': i + 1,
                        'additional_info': f"Transformation applied: {log_entry['transformation_applied']}"
                    })

                    # Small delay to prevent Excel file corruption
                    time.sleep(0.05)

                # Small delay to prevent Excel file corruption
                time.sleep(0.1)

                # Log to Pipeline_Preparation sheet with full input/output
                self.log_pipeline_preparation_to_excel(excel_path, {
                    'operation': 'PRE_EXECUTION',
                    'statement_number': current_statement_index + 1,
                    'source_statement_number': source_statement_number,
                    'attempt_number': getattr(state, 'current_attempt', 1),
                    'original_input': original_statement,  # Full original statement
                    'pre_processed_output': current_output,  # Full pre-processed output
                    'execution_log': execution_log,
                    'modules_executed': len(pre_execution_modules)
                })

            print(f"✅ Pre-feature execution completed")
            print(f"📤 Pre-processed output: {current_output}")

            return {
                "pre_processed_output": current_output,
                "execution_log": execution_log,
                "success": True
            }

        except Exception as e:
            print(f"❌ Error in execute_pre_features: {str(e)}")
            return {"success": False, "error": str(e)}

    def get_post_processing_features(self, current_statement: Dict[str, Any]) -> List[tuple]:
        """Get post-processing features from the post_features column in CSV."""
        # Get post_features directly from the CSV data
        post_features = current_statement.get('post_features', [])

        # Ensure it's a list of tuples
        if isinstance(post_features, list):
            return post_features
        else:
            return []

    def categorize_modules_by_execution_order(self, available_features: List[tuple],
                                            responsible_features: List[tuple],
                                            post_features: List[tuple]) -> Dict[str, List[tuple]]:
        """
        Dynamic categorization based on AI identification and complete pipeline order.

        Logic:
        - Complete pipeline: available_features + post_features (natural execution order)
        - AI identifies responsible modules from complete pipeline
        - Pre-execution: Modules before first responsible module in complete pipeline
        - Responsible: Only AI-identified modules (for enhancement)
        - Post-execution: Modules after first responsible, excluding AI-identified ones
        """

        # Get AI-identified responsible modules with full tuple matching
        ai_identified_tuples = []
        for feat in responsible_features:
            if isinstance(feat, tuple) and len(feat) >= 2:
                ai_identified_tuples.append((feat[0], feat[1]))  # (feature_name, module_path)
            elif isinstance(feat, dict):
                ai_identified_tuples.append((feat.get('module_name', ''), feat.get('module_path', '')))

        print(f"📋 Dynamic categorization:")
        print(f"   🔗 Available features: {[(f[0], f[1]) if isinstance(f, tuple) and len(f) >= 2 else str(f) for f in available_features]}")
        print(f"   📤 Post features: {[(f[0], f[1]) if isinstance(f, tuple) and len(f) >= 2 else str(f) for f in post_features]}")
        print(f"   🎯 AI identified: {ai_identified_tuples}")

        # Create complete pipeline order (available + post)
        complete_pipeline = available_features + post_features
        complete_pipeline_tuples = [(f[0], f[1]) if isinstance(f, tuple) and len(f) >= 2 else (str(f), '') for f in complete_pipeline]

        print(f"   🔗 Complete pipeline order: {complete_pipeline_tuples}")

        # Find first responsible module position in complete pipeline using tuple matching
        responsible_positions = []
        for ai_tuple in ai_identified_tuples:
            try:
                pos = complete_pipeline_tuples.index(ai_tuple)
                responsible_positions.append(pos)
            except ValueError:
                print(f"   ⚠️ AI identified module '{ai_tuple}' not found in complete pipeline")

        if not responsible_positions:
            print(f"   ⚠️ No AI-identified modules found in pipeline - skipping to next statement")
            return {
                'pre_execution': [],
                'responsible': [],
                'post_execution': []
            }

        first_responsible_index = min(responsible_positions)

        # Dynamic categorization based on AI identification
        pre_execution = complete_pipeline[:first_responsible_index]
        responsible = [feat for feat in responsible_features]  # Only AI-identified modules (full tuples)
        post_execution = [
            complete_pipeline[i] for i in range(first_responsible_index, len(complete_pipeline))
            if complete_pipeline_tuples[i] not in ai_identified_tuples
        ]

        print(f"   📋 Pre-execution: {[f[0] if isinstance(f, tuple) and len(f) >= 1 else str(f) for f in pre_execution]}")
        print(f"   🎯 Responsible: {[f[0] if isinstance(f, tuple) and len(f) >= 1 else str(f) for f in responsible]}")
        print(f"   📤 Post-execution: {[f[0] if isinstance(f, tuple) and len(f) >= 1 else str(f) for f in post_execution]}")

        return {
            'pre_execution': pre_execution,
            'responsible': responsible,
            'post_execution': post_execution
        }

    def log_pipeline_preparation_to_excel(self, excel_path: str, data: Dict[str, Any]) -> None:
        """Log pipeline preparation results (categorization + pre-execution) to Excel."""
        try:
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            # Get source statement number
            statement_number = data.get('statement_number', 0)
            source_statement_number = data.get('source_statement_number', statement_number)

            excel_data = [{
                "Statement_ID": statement_number,
                "Source_Statement_Number": source_statement_number,
                "Attempt_Number": data.get('attempt_number', 1),
                "Operation": data.get('operation', ''),
                "Details": str(data.get('categorization_summary', data.get('modules_executed', ''))),
                "Input": data.get('original_input', ''),  # Full input, no truncation
                "Output": data.get('pre_processed_output', ''),  # Full output, no truncation
                "Execution_Log": str(data.get('execution_log', data.get('module_categories', ''))),
                "Timestamp": current_timestamp
            }]

            append_sheet_to_excel(excel_path, "Pipeline_Preparation", excel_data)
            print(f"📊 Logged {data.get('operation')} to Pipeline_Preparation sheet")

        except Exception as e:
            print(f"❌ Error logging pipeline preparation to Excel: {str(e)}")

    def combine_driver_module(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Combine Driver Module - Original Modules Only

        Purpose:
            Combine original responsible modules into single driver module for enhancement.
            This node specifically handles original modules from the file system.

        Use Cases:
            - First time enhancement (continue_enhancement path)
            - Enhancement retry (retry_original path)

        Input: Original modules from responsible_features
        Output: Combined original modules ready for AI enhancement
        """
        print("\n" + "="*80)
        print("🤖 COMBINE DRIVER MODULE")
        print("="*80)

        try:
            # Get AI-identified responsible modules only
            responsible_modules = getattr(state, 'responsible_features', [])

            print(f"🔍 Combining {len(responsible_modules)} AI-identified responsible modules")
            print(f"   🎯 Modules to enhance: {[(rf[0], rf[1]) if isinstance(rf, tuple) and len(rf) >= 2 else str(rf) for rf in responsible_modules]}")

            if not responsible_modules:
                print(f"   ⚠️ No responsible modules to combine")
                return {"combination_success": False, "combined_driver_code": ""}

            # Read and combine AI-identified modules
            combined_modules = []
            attempt_number = getattr(state, 'current_attempt', 1)

            for module_data in responsible_modules:
                # Handle different data structures
                if isinstance(module_data, tuple) and len(module_data) >= 2:
                    module_name = module_data[0]
                    module_path = module_data[1]
                elif isinstance(module_data, dict):
                    module_name = module_data.get('module_name', '')
                    module_path = module_data.get('module_path', '')
                else:
                    print(f"⚠️ Unexpected module data format: {module_data}")
                    continue
                # Load original module (primary purpose of combine_driver_module)
                print(f"📁 Loading original module: {module_name}")
                module_code = self.read_and_decrypt_module(module_name, module_path,
                                                         state.migration_name, state.cloud_category, "driver_combination")

                if module_code:
                    # Add boundary markers
                    module_with_markers = f"""
# === MODULE: {module_name} START ===
{module_code}
# === MODULE: {module_name} END ===
"""
                    combined_modules.append(module_with_markers)
                    print(f"   ✅ Added {module_name} to driver module")

            # Combine all modules into single driver
            driver_module_code = "\n\n".join(combined_modules)

            # Save driver module
            driver_module_path = self.save_driver_module(driver_module_code, attempt_number, state)

            # Log to Excel using new hierarchical system
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                # Extract module names safely from different data structures
                module_names = []
                for module_data in responsible_modules:
                    if isinstance(module_data, tuple) and len(module_data) >= 1:
                        module_names.append(module_data[0])
                    elif isinstance(module_data, dict):
                        module_names.append(module_data.get('module_name', ''))

                # Get module counts from state
                module_categories = getattr(state, 'module_categories', {})
                pre_count = len(module_categories.get('pre_execution', []))
                responsible_count = len(module_categories.get('responsible', []))
                post_count = len(module_categories.get('post_execution', []))
                total_modules = pre_count + responsible_count + post_count

                # Get source statement number using dedicated function
                source_statement_number = self.get_current_source_statement_number(state)

                # Log pipeline overview with correct data
                self.log_pipeline_overview(excel_path, {
                    'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                    'source_statement_number': source_statement_number,
                    'attempt_number': attempt_number,
                    'total_modules': total_modules,
                    'pre_count': pre_count,
                    'responsible_count': responsible_count,
                    'post_count': post_count,
                    'overall_status': 'DRIVER_COMBINATION_COMPLETE',
                    'pipeline_phase': 'Driver Combination',
                    'success': True,
                    'notes': f"Combined {responsible_count} responsible modules: {', '.join(module_names)}. Total pipeline: {pre_count} pre + {responsible_count} responsible + {post_count} post = {total_modules} modules"
                })

                # Log each module combination
                for i, module_data in enumerate(responsible_modules):
                    module_name = module_data[0] if isinstance(module_data, tuple) else module_data.get('module_name', '')
                    module_path = module_data[1] if isinstance(module_data, tuple) and len(module_data) >= 2 else module_data.get('module_path', '')

                    # Get source statement number using dedicated function
                    source_statement_number = self.get_current_source_statement_number(state)

                    self.log_module_processing(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': attempt_number,
                        'operation': 'DRIVER_COMBINATION',
                        'module_name': module_name,
                        'module_type': 'responsible',
                        'input_code': f"Original module from: {module_path}",
                        'output_code': driver_module_code,  # Full combined driver code
                        'status': 'COMBINED',
                        'changes_made': True,
                        'file_path': driver_module_path,
                        'module_order': i + 1,
                        'additional_info': f"Combined into driver module with boundary markers"
                    })

            print(f"✅ Driver module combination completed")
            print(f"📁 Driver module saved: {driver_module_path}")

            return {
                "driver_module_code": driver_module_code,
                "driver_module_path": driver_module_path,
                "combined_modules_count": len(responsible_modules),
                "success": True
            }

        except Exception as e:
            print(f"❌ Error in combine_driver_module: {str(e)}")
            return {"success": False, "error": str(e)}

    def combine_enhanced_modules(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Combine Enhanced Modules for Re-enhancement

        This function handles combining enhanced modules when execution fails from enhanced sources.
        It loads enhanced modules from registry when they need to be re-enhanced with feedback.

        Purpose:
            - Load enhanced modules that failed during execution
            - Prepare them for re-enhancement with feedback
            - Handle both registry_failed and mixed_failed scenarios

        Input Requirements:
            - state.responsible_features: List of responsible modules to load
            - state.execution_source: Should be registry_failed or mixed_failed
            - state.migration_name, state.cloud_category: For module loading

        Output:
            - driver_module_code: Enhanced modules combined for re-enhancement
            - driver_module_path: Path to saved combined module

        Next Node:
            - enhance_driver_module
        """
        print("\n" + "="*80)
        print("🗂️ COMBINE ENHANCED MODULES")
        print("="*80)

        try:
            # Get required data
            responsible_features = getattr(state, 'responsible_features', [])
            execution_source = getattr(state, 'execution_source', 'enhancement')
            attempt_number = getattr(state, 'current_attempt', 1)

            print(f"🔄 Loading enhanced modules for re-enhancement")
            print(f"   - Execution source: {execution_source}")
            print(f"   - Responsible features: {len(responsible_features)}")
            print(f"   - Attempt: {attempt_number}")

            if not responsible_features:
                return {"success": False, "error": "No responsible features available for enhanced module combination"}

            combined_modules = []

            # Smart loading based on execution source
            for feature_data in responsible_features:
                if len(feature_data) >= 2:
                    module_name = feature_data[0]
                    module_path = feature_data[1]

                    # Initialize module_code to None for each iteration
                    module_code = None
                    is_from_registry = False

                    if execution_source in ['registry_failed', 'registry']:
                        # All modules should be from registry
                        print(f"🗂️ Loading enhanced module from registry: {module_name}")
                        module_code = load_enhanced_module_from_registry(module_path, state.migration_name, state.cloud_category)
                        is_from_registry = True
                    elif execution_source in ['mixed_failed', 'mixed']:
                        # Check registry first, then original (store result to avoid duplicate check)
                        is_from_registry = check_registry_for_module(module_path, state.migration_name, state.cloud_category)
                        if is_from_registry:
                            print(f"🗂️ Loading enhanced module from registry: {module_name}")
                            module_code = load_enhanced_module_from_registry(module_path, state.migration_name, state.cloud_category)
                        else:
                            print(f"📁 Loading original module: {module_name}")
                            module_code = self.read_and_decrypt_module(module_name, module_path, state.migration_name, state.cloud_category, "enhanced_combination")
                    else:
                        # Default fallback for any other execution source
                        print(f"⚠️ Unknown execution source '{execution_source}', loading original module: {module_name}")
                        module_code = self.read_and_decrypt_module(module_name, module_path, state.migration_name, state.cloud_category, "fallback_combination")

                    if module_code:
                        # Add boundary markers (same as combine_driver_module)
                        module_with_markers = f"""
# === MODULE: {module_name} START ===
{module_code}
# === MODULE: {module_name} END ===
"""
                        combined_modules.append(module_with_markers)
                        print(f"   ✅ Added {module_name} to driver module")
                    else:
                        print(f"   ❌ Module {module_name} failed to load")

            if not combined_modules:
                return {"success": False, "error": "No enhanced modules could be loaded from registry"}

            # Combine all enhanced modules into single driver
            driver_module_code = "\n\n".join(combined_modules)

            # Save driver module (same as combine_driver_module)
            driver_module_path = self.save_driver_module(driver_module_code, attempt_number, state)

            # Excel logging (same as combine_driver_module)
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                # Collect module names for logging
                module_names = []
                for feature_data in responsible_features:
                    if len(feature_data) >= 2:
                        module_names.append(feature_data[0])

                # Get module counts from state
                module_categories = getattr(state, 'module_categories', {})
                pre_count = len(module_categories.get('pre_execution', []))
                responsible_count = len(responsible_features)
                post_count = len(module_categories.get('post_execution', []))
                total_modules = pre_count + responsible_count + post_count

                # Get source statement number using dedicated function
                source_statement_number = self.get_current_source_statement_number(state)

                # Log pipeline overview with correct data
                self.log_pipeline_overview(excel_path, {
                    'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                    'source_statement_number': source_statement_number,
                    'attempt_number': attempt_number,
                    'total_modules': total_modules,
                    'pre_count': pre_count,
                    'responsible_count': responsible_count,
                    'post_count': post_count,
                    'overall_status': 'ENHANCED_DRIVER_COMBINATION_COMPLETE',
                    'pipeline_phase': 'Enhanced Driver Combination',
                    'success': True,
                    'notes': f"Combined {responsible_count} enhanced/mixed modules: {', '.join(module_names)}. Execution source: {execution_source}. Total pipeline: {pre_count} pre + {responsible_count} responsible + {post_count} post = {total_modules} modules"
                })

                # Log each module combination
                for i, feature_data in enumerate(responsible_features):
                    if len(feature_data) >= 2:
                        module_name = feature_data[0]
                        module_path = feature_data[1]

                        # Determine module source for logging (using stored registry check result)
                        if execution_source in ['registry_failed', 'registry']:
                            input_source = f"Enhanced module from registry: {module_path}"
                        elif execution_source in ['mixed_failed', 'mixed']:
                            if is_from_registry:
                                input_source = f"Enhanced module from registry: {module_path}"
                            else:
                                input_source = f"Original module from: {module_path}"
                        else:
                            input_source = f"Module from: {module_path}"

                        # Get source statement number using dedicated function
                        source_statement_number = self.get_current_source_statement_number(state)

                        self.log_module_processing(excel_path, {
                            'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                            'source_statement_number': source_statement_number,
                            'attempt_number': attempt_number,
                            'operation': 'ENHANCED_DRIVER_COMBINATION',
                            'module_name': module_name,
                            'module_type': 'responsible',
                            'input_code': input_source,
                            'output_code': driver_module_code,  # Full combined driver code
                            'status': 'COMBINED',
                            'changes_made': True,
                            'file_path': driver_module_path,
                            'module_order': i + 1,
                            'additional_info': f"Combined into enhanced driver module with boundary markers. Source: {execution_source}"
                        })

            print(f"✅ Enhanced driver module combination completed")
            print(f"📁 Driver module saved: {driver_module_path}")

            # CRITICAL: Update execution_source to 'enhancement' for proper attempt tracking
            # This ensures that subsequent AI comparison failures will route to enhancement_iteration_control
            print("🔄 Transitioning from direct execution to enhancement pipeline")
            print(f"   - Previous execution_source: {execution_source}")
            print(f"   - New execution_source: enhancement")

            return {
                "driver_module_code": driver_module_code,
                "driver_module_path": driver_module_path,
                "combined_modules_count": len(combined_modules),
                "execution_source": "enhancement",  # Update execution source for proper routing
                "success": True
            }

        except Exception as e:
            print(f"❌ Error in combine_enhanced_modules: {str(e)}")
            return {"success": False, "error": str(e)}

    def enhance_driver_module(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 9: Enhance Driver Module - AI-Driven Module Enhancement Hub

        Purpose:
            Central enhancement node that uses AI to improve combined driver modules based on
            comprehensive feedback from validation, execution, and comparison failures.
            This is the primary retry target for all pipeline validation loops.

        Business Logic:
            1. Receives feedback from validate_module_enhancement, execute_complete_pipeline,
               and ai_statement_comparison_pipeline nodes
            2. Uses AI to analyze feedback and enhance module logic accordingly
            3. Implements iterative improvement based on specific failure patterns
            4. Maintains enhancement history for learning from previous attempts

        Enhancement Process:
            - Analyzes combined driver module code from previous step
            - Incorporates responsibility context from feature identification
            - Applies validation feedback (syntax, logic errors)
            - Applies execution feedback (runtime errors, deployment issues)
            - Applies comparison feedback (functional equivalence failures)
            - Uses deployment error context for targeted fixes

        Feedback Integration:
            - validation_feedback: Syntax and logic validation errors
            - execution_feedback: Runtime and deployment errors
            - comparison_feedback: Functional equivalence analysis
            - responsibility_context: Original AI analysis reasoning

        Input Requirements:
            - state.combined_driver_code: Combined module code to enhance
            - state.pre_processed_output: Pre-processing results for context
            - Various feedback fields from validation/execution/comparison failures
            - state.current_attempt: Attempt counter for enhancement tracking

        Output:
            - enhanced_driver_code: AI-enhanced module code
            - enhancement_summary: Summary of changes made
            - attempt_number: Updated attempt counter
            - enhancement_reasoning: AI explanation of improvements

        Next Nodes:
            - Success → decompose_enhanced_module
            - Enhancement failure → enhancement_iteration_control

        Error Handling:
            - AI enhancement failures with fallback strategies
            - Code generation errors
            - Feedback parsing errors
        """
        print("\n" + "="*80)
        print("🔧 ENHANCE DRIVER MODULE")
        print("="*80)

        try:
            # Always use original driver module code for fresh enhancement
            # This avoids reusing potentially broken enhanced code
            driver_module_code = getattr(state, 'driver_module_code', '')
            print("🔧 Using original driver module for fresh enhancement with feedback")

            if not driver_module_code:
                return {"success": False, "error": "No driver module code available"}

            # Build enhancement context
            current_statement_index = getattr(state, 'current_statement_index', 0)
            available_features_with_statements = getattr(state, 'available_features_with_statements', [])
            current_statement = available_features_with_statements[current_statement_index]

            # Get responsible features for context
            responsible_features = getattr(state, 'responsible_features', [])

            # Build responsibility context for prompt compatibility
            responsible_modules_with_reasoning = []
            for feature_tuple in responsible_features:
                if len(feature_tuple) >= 3:
                    module_info = {
                        'module_name': feature_tuple[0],
                        'module_path': feature_tuple[1],
                        'responsibility_reason': feature_tuple[2],
                        'keywords': feature_tuple[3] if len(feature_tuple) > 3 else ''
                    }
                    responsible_modules_with_reasoning.append(module_info)

            # Get pre-processed output from state (critical for enhancement)
            pre_processed_output = getattr(state, 'pre_processed_output',
                                         current_statement.get('statement_after_typecasting', ''))

            # Uniform enhancement context with standardized field names
            enhancement_context = {
                # Core transformation context (used by sequential_enhancement_prompt)
                'original_input_statement': current_statement.get('statement_after_typecasting', ''),
                'pre_processed_input': pre_processed_output,  # Current state after pre-processing
                'expected_final_output': current_statement.get('ai_converted_statement', ''),

                # Error and deployment context
                'deployment_error': current_statement.get('original_deployment_error', ''),
                'current_attempt': getattr(state, 'current_attempt', 1),

                # Module execution context
                'pre_execution_modules': [mod[0] for mod in getattr(state, 'module_categories', {}).get('pre_execution', [])],

                # Responsibility context (used by build_responsibility_section)
                'responsible_modules_with_reasoning': responsible_modules_with_reasoning,
                'responsibility_summary': f"Identified {len(responsible_modules_with_reasoning)} responsible modules for enhancement",

                # Database terms (dynamic from migration_name)
                'db_terms': get_database_specific_terms(state.migration_name),

                # Output comparison context (using correct available data)
                'ai_corrected_output': current_statement.get('ai_converted_statement', ''),
                'original_failed_output': current_statement.get('original_target_statement', ''),
                # Attempt history for AI comparison learning (maintained across attempts)
                'attempt_history': getattr(state, 'attempt_history', [])
            }

            # COMBINED FEEDBACK INTEGRATION - Include all available feedback
            feedback_sources = []

            # Always add attempt history if available (for learning)
            if hasattr(state, 'attempt_history'):
                enhancement_context['attempt_history'] = getattr(state, 'attempt_history', [])

            # Always add ai_comparison_feedback if available (includes transformation guidance)
            if hasattr(state, 'ai_comparison_feedback') and state.ai_comparison_feedback:
                enhancement_context['ai_comparison_feedback'] = state.ai_comparison_feedback
                feedback_sources.append('comparison')

            # Determine primary feedback type for logging (most recent failure)
            primary_feedback_type = None
            if hasattr(state, 'validation_feedback') and state.validation_feedback:
                primary_feedback_type = 'validation'
            elif hasattr(state, 'execution_feedback') and state.execution_feedback:
                primary_feedback_type = 'execution'
            elif hasattr(state, 'ai_comparison_feedback') and state.ai_comparison_feedback:
                primary_feedback_type = 'comparison'

            # Process all available feedback types for comprehensive enhancement
            # Add validation feedback if available
            if hasattr(state, 'validation_feedback') and state.validation_feedback:
                enhancement_context['validation_feedback'] = state.validation_feedback
                if 'validation' not in feedback_sources:
                    feedback_sources.append('validation')

            # Add execution feedback if available
            if hasattr(state, 'execution_feedback') and state.execution_feedback:
                enhancement_context['execution_feedback'] = state.execution_feedback
                if 'execution' not in feedback_sources:
                    feedback_sources.append('execution')

            # Set attempt number and execution context
            attempt_number = getattr(state, 'current_attempt', 1)
            execution_source = getattr(state, 'execution_source', 'enhancement')

            # Print detailed feedback information - ALWAYS show ALL available feedback types
            print(f"🔄 COMPREHENSIVE FEEDBACK FOR ENHANCEMENT (Primary: {primary_feedback_type or 'none'})")
            print(f"   🎯 Execution Source: {execution_source}")
            print(f"   📈 Attempt Number: {attempt_number}")
            print(f"   📊 Available Feedback Sources: {feedback_sources if feedback_sources else ['none']}")

            # Show context based on execution source and attempt
            if execution_source in ['registry_failed', 'mixed_failed']:
                print(f"   🔄 Context: Direct {execution_source.replace('_failed', '')} execution failed - enhancing with enhanced modules")
            elif attempt_number == 1 and not feedback_sources:
                print(f"   🆕 Context: First enhancement attempt - no previous feedback available")
            elif attempt_number == 1 and feedback_sources:
                print(f"   🔄 Context: First enhancement attempt with feedback from previous validation/execution failures")
            else:
                print(f"   🔄 Context: Retry attempt {attempt_number} - using comprehensive feedback from previous failures")

            # Show validation feedback if available
            if hasattr(state, 'validation_feedback') and state.validation_feedback:
                print(f"📋 VALIDATION FEEDBACK (SYNTAX ERRORS - HIGH PRIORITY):")
                print(f"   ❌ Failed Modules: {state.validation_feedback.get('failed_modules', [])}")
                print(f"   🐛 Validation Errors: {state.validation_feedback.get('validation_errors', [])}")
                print(f"   🔧 Retry Guidance: {state.validation_feedback.get('retry_guidance', '')}")

            # Show execution feedback if available
            if hasattr(state, 'execution_feedback') and state.execution_feedback:
                print(f"📋 EXECUTION FEEDBACK (RUNTIME ERRORS - HIGH PRIORITY):")
                print(f"   💥 Execution Errors: {state.execution_feedback.get('execution_errors', [])}")
                print(f"   📍 Failed Phase: {state.execution_feedback.get('failed_phase', '')}")
                print(f"   🔧 Retry Guidance: {state.execution_feedback.get('retry_guidance', '')}")

            # Show AI comparison feedback if available (CRITICAL for transformation guidance)
            if hasattr(state, 'ai_comparison_feedback') and state.ai_comparison_feedback:
                print(f"📋 AI COMPARISON FEEDBACK (TRANSFORMATION GUIDANCE - CRITICAL CONTEXT):")
                print(f"   🔍 Mismatch Explanation: {state.ai_comparison_feedback.get('mismatch_explanation', '')}")
                print(f"   🎯 Expected Output: {state.ai_comparison_feedback.get('expected_output', '')}")
                print(f"   📝 Actual Output: {state.ai_comparison_feedback.get('final_output', '')}")
                print(f"   🔧 Retry Guidance: {state.ai_comparison_feedback.get('retry_guidance', '')}")

                # Show transformation guidance details if available
                transformation_guidance = state.ai_comparison_feedback.get('transformation_guidance', {})
                if transformation_guidance and transformation_guidance.get('required'):
                    print(f"   🛠️ TRANSFORMATION GUIDANCE AVAILABLE:")
                    specific_changes = transformation_guidance.get('specific_changes', [])
                    if specific_changes:
                        print(f"      📝 Specific Changes Required:")
                        for i, change in enumerate(specific_changes, 1):
                            source_pattern = change.get('source_pattern', '')
                            target_pattern = change.get('target_pattern', '')
                            print(f"         {i}. {source_pattern} → {target_pattern}")

                    implementation_steps = transformation_guidance.get('implementation_steps', [])
                    if implementation_steps:
                        print(f"      📋 Implementation Steps:")
                        for i, step in enumerate(implementation_steps, 1):
                            print(f"         {i}. {step}")

            print(f"   🎯 Enhancement Strategy: Fix ALL issues using comprehensive feedback")
            print(f"   📈 Attempt Number: {attempt_number} (fresh enhancement with all available feedback)")

            # Always show attempt history if available (regardless of primary feedback type)
            attempt_history = getattr(state, 'attempt_history', [])
            if attempt_history:
                print(f"\n📚 ATTEMPT HISTORY BEING PASSED TO AI ENHANCEMENT:")
                print(f"   📊 Total Previous Attempts: {len(attempt_history)}")
                for i, attempt in enumerate(attempt_history, 1):
                    print(f"   📋 Attempt {i} Details:")
                    print(f"      ⏰ Timestamp: {attempt.get('timestamp', 'No timestamp')}")
                    print(f"      📊 Status: {attempt.get('status', 'Unknown')}")
                    print(f"      🔧 Modules Used: {len(attempt.get('modules_used', []))}")
                    modules_names = [mod.get('module_name', 'unknown') for mod in attempt.get('modules_used', [])]
                    print(f"      📦 Module Names: {modules_names}")
                    print(f"      🤖 AI Feedback (FULL CONTENT):")
                    print(f"         {attempt.get('ai_feedback', 'No feedback')}")
                    print(f"      📝 Final Output Length: {len(attempt.get('final_output', ''))}")
                    if attempt.get('final_output'):
                        print(f"      📄 Final Output (FULL CONTENT):")
                        print(f"         {attempt.get('final_output', '')}")
            else:
                print(f"\n📚 ATTEMPT HISTORY: No previous attempts available")

            # Always show detailed AI comparison feedback if available (regardless of primary feedback type)
            if hasattr(state, 'ai_comparison_feedback') and state.ai_comparison_feedback:
                print(f"\n🧠 DETAILED AI COMPARISON FEEDBACK BEING PASSED:")
                if isinstance(state.ai_comparison_feedback, dict):
                    for key, value in state.ai_comparison_feedback.items():
                        if key in ['final_output', 'expected_output']:
                            print(f"   📝 {key}: {len(str(value))} chars")
                            print(f"      FULL CONTENT:")
                            print(f"         {str(value)}")
                        elif key == 'transformation_guidance':
                            print(f"   📋 {key} (FULL CONTENT):")
                            # Format transformation guidance nicely instead of raw dictionary
                            if isinstance(value, dict) and value.get('required'):
                                print(f"      🛠️ MODULE-SPECIFIC TRANSFORMATION GUIDANCE:")
                                specific_changes = value.get('specific_changes', [])
                                for i, change in enumerate(specific_changes, 1):
                                    print(f"         {i}. Source Pattern: {change.get('source_pattern', '')}")
                                    print(f"            Target Pattern: {change.get('target_pattern', '')}")
                                    print(f"            Module Guidance: {change.get('transformation_method', '')}")
                                    print(f"            Variations: {change.get('variations_to_handle', [])}")

                                implementation_steps = value.get('implementation_steps', [])
                                if implementation_steps:
                                    print(f"      📋 Implementation Steps:")
                                    for i, step in enumerate(implementation_steps, 1):
                                        print(f"         {i}. {step}")

                                parameter_mapping = value.get('parameter_mapping', {})
                                if parameter_mapping:
                                    print(f"      🔧 Parameter Mapping:")
                                    for param_key, param_value in parameter_mapping.items():
                                        print(f"         {param_key}: {param_value}")
                            else:
                                print(f"      {value}")
                        else:
                            print(f"   📋 {key} (FULL CONTENT):")
                            print(f"      {value}")

            # Show summary of what's being passed to AI
            if not primary_feedback_type:
                print("\n🆕 FIRST ATTEMPT - No specific feedback, using original enhancement approach")

            # Print attempt history summary
            attempt_history = getattr(state, 'attempt_history', [])
            if attempt_history:
                print(f"   📚 Attempt History: {len(attempt_history)} attempts")
            else:
                print(f"   📚 Attempt History: No previous attempts")

            # AI Enhancement
            print(f"🤖 Starting AI enhancement of driver module")
            print(f"📝 Pre-processed input length: {len(enhancement_context['pre_processed_input'])}")
            print(f"🎯 Expected output length: {len(enhancement_context['expected_final_output'])}")

            # Print comprehensive feedback summary being passed to AI
            print(f"\n🔍 FEEDBACK BEING PASSED TO AI ENHANCEMENT:")
            print(f"   📊 Feedback Sources: {feedback_sources}")
            if 'validation' in feedback_sources:
                print(f"   ✅ Validation Feedback: {enhancement_context.get('validation_feedback', {})}")
            if 'execution' in feedback_sources:
                print(f"   ⚡ Execution Feedback: {enhancement_context.get('execution_feedback', {})}")
            if 'comparison' in feedback_sources:
                print(f"   🧠 AI Comparison Feedback: Available with transformation guidance")
            if 'attempt_history' in enhancement_context:
                print(f"   📚 Attempt History: {len(enhancement_context['attempt_history'])} attempts")

            enhanced_driver_code = self.ai_enhance_driver_module(driver_module_code, enhancement_context)

            # Save enhanced driver module (overwrites during feedback loops)
            attempt_number = getattr(state, 'current_attempt', 1)
            enhanced_driver_path = self.save_enhanced_driver_module(enhanced_driver_code, attempt_number, state)

            # Log to Excel using new hierarchical system
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                # Get module counts from state for consistent data
                module_categories = getattr(state, 'module_categories', {})
                pre_count = len(module_categories.get('pre_execution', []))
                responsible_count = len(module_categories.get('responsible', []))
                post_count = len(module_categories.get('post_execution', []))
                total_modules = pre_count + responsible_count + post_count

                # Get source statement number using dedicated function
                source_statement_number = self.get_current_source_statement_number(state)

                # Log pipeline overview update with correct data
                self.log_pipeline_overview(excel_path, {
                    'statement_number': current_statement_index + 1,
                    'source_statement_number': source_statement_number,
                    'attempt_number': attempt_number,
                    'total_modules': total_modules,
                    'pre_count': pre_count,
                    'responsible_count': responsible_count,
                    'post_count': post_count,
                    'overall_status': 'DRIVER_ENHANCEMENT_COMPLETE',
                    'pipeline_phase': 'Driver Enhancement',
                    'success': True,
                    'notes': f"Enhanced driver using feedback sources: {', '.join(feedback_sources) if feedback_sources else 'None'}. Total pipeline: {pre_count} pre + {responsible_count} responsible + {post_count} post = {total_modules} modules"
                })

                # Log module processing for enhancement
                self.log_module_processing(excel_path, {
                    'statement_number': current_statement_index + 1,
                    'source_statement_number': source_statement_number,
                    'attempt_number': attempt_number,
                    'operation': 'DRIVER_ENHANCEMENT',
                    'module_name': 'combined_driver',
                    'module_type': 'responsible',
                    'input_code': driver_module_code,  # Full original driver code
                    'output_code': enhanced_driver_code,  # Full enhanced driver code
                    'status': 'ENHANCED',
                    'changes_made': driver_module_code != enhanced_driver_code,
                    'file_path': enhanced_driver_path,
                    'additional_info': f"AI enhancement using feedback: {', '.join(feedback_sources) if feedback_sources else 'Initial enhancement'}"
                })

                # Log feedback if any was used
                if feedback_sources:
                    feedback_content = ""
                    if 'validation' in feedback_sources and hasattr(state, 'validation_feedback'):
                        feedback_content = str(state.validation_feedback)
                    elif 'execution' in feedback_sources and hasattr(state, 'execution_feedback'):
                        feedback_content = str(state.execution_feedback)
                    elif 'comparison' in feedback_sources and hasattr(state, 'ai_comparison_feedback'):
                        feedback_content = str(state.ai_comparison_feedback)

                    self.log_enhancement_feedback(excel_path, {
                        'statement_number': current_statement_index + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': attempt_number,
                        'feedback_type': feedback_sources[0] if feedback_sources else 'none',
                        'source_node': 'enhance_driver_module',
                        'feedback_content': feedback_content,
                        'retry_guidance': 'Applied feedback to enhance driver module',
                        'next_action': 'decompose_enhanced_module',
                        'priority': 'high' if 'validation' in feedback_sources else 'medium'
                    })

            print(f"✅ Driver module enhancement completed")
            print(f"📁 Enhanced driver saved: {enhanced_driver_path}")
            print(f"🔄 Feedback sources used: {', '.join(feedback_sources) if feedback_sources else 'None'}")

            # Note: Feedback clearing is handled by source nodes:
            # - Validation feedback: Cleared by validate_module_enhancement when validation succeeds
            # - Execution feedback: Cleared by execute_complete_pipeline when execution succeeds
            # - AI comparison feedback: Never cleared here - maintained for attempt history

            # Create updated_modules for attempt history tracking
            updated_modules = [{
                'module_name': 'enhanced_driver_module',
                'module_type': 'driver',
                'file_path': enhanced_driver_path,
                'code_content': enhanced_driver_code,
                'feedback_sources': feedback_sources,
                'changes_made': driver_module_code != enhanced_driver_code,
                'attempt_number': attempt_number
            }]

            return {
                "enhanced_driver_code": enhanced_driver_code,
                "enhanced_driver_path": enhanced_driver_path,
                "original_driver_code": driver_module_code,
                "feedback_sources": feedback_sources,
                "updated_modules": updated_modules,  # ✅ Add this for attempt history
                "success": True
            }

        except Exception as e:
            print(f"❌ Error in enhance_driver_module: {str(e)}")
            return {"success": False, "error": str(e)}

    # ==================== DRIVER MODULE HELPER FUNCTIONS ====================

    def save_driver_module(self, driver_code: str, attempt_number: int, state: Stage2WorkflowState) -> str:
        """Save combined driver module with simplified naming: driver_attempt_{attempt_number}.py"""
        try:
            # Get source statement number for current statement
            source_statement_number = self.get_current_source_statement_number(state)

            # Get feature modules directory using source statement number
            paths_info = self.setup_module_update_paths(
                state,
                source_statement_number,
                attempt_number
            )
            feature_modules_dir = paths_info['feature_modules_dir']

            # Create directory if needed
            os.makedirs(feature_modules_dir, exist_ok=True)


            # Save driver module with simplified naming
            driver_filename = f"driver_attempt_{attempt_number}.py"
            driver_path = os.path.join(feature_modules_dir, driver_filename)

            with open(driver_path, 'w', encoding='utf-8') as file:
                file.write(driver_code)

            print(f"💾 Saved driver module: {driver_path}")
            return driver_path

        except Exception as e:
            print(f"❌ Error saving driver module: {str(e)}")
            return ""

    def save_enhanced_driver_module(self, enhanced_code: str, attempt_number: int, state: Stage2WorkflowState) -> str:
        """Save enhanced driver module with simplified naming: enhanced_attempt_{attempt_number}.py"""
        try:
            # Get source statement number for current statement
            source_statement_number = self.get_current_source_statement_number(state)

            # Get feature modules directory using source statement number
            paths_info = self.setup_module_update_paths(
                state,
                source_statement_number,
                attempt_number
            )
            feature_modules_dir = paths_info['feature_modules_dir']

            # Create directory if needed
            os.makedirs(feature_modules_dir, exist_ok=True)

            # Save enhanced driver module with simplified naming (overwrites during feedback)
            enhanced_filename = f"enhanced_attempt_{attempt_number}.py"
            enhanced_path = os.path.join(feature_modules_dir, enhanced_filename)

            with open(enhanced_path, 'w', encoding='utf-8') as file:
                file.write(enhanced_code)

            print(f"💾 Saved enhanced driver module: {enhanced_path}")
            return enhanced_path

        except Exception as e:
            print(f"❌ Error saving enhanced driver module: {str(e)}")
            return ""

    def ai_enhance_driver_module(self, driver_code: str, enhancement_context: Dict[str, Any]) -> str:
        """AI enhancement of combined driver module."""
        try:
            # Use existing AI enhancement logic but for combined module
            # This leverages the existing AI enhancement infrastructure
            enhanced_code = self.ai_enhance_with_sequential_context(driver_code, enhancement_context)
            return enhanced_code

        except Exception as e:
            print(f"❌ Error in AI driver enhancement: {str(e)}")
            return driver_code  # Return original on error

    # ==================== NEW HIERARCHICAL EXCEL LOGGING SYSTEM ====================

    def log_pipeline_overview(self, excel_path: str, data: Dict[str, Any]) -> None:
        """Log high-level pipeline overview to Excel."""
        try:
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            # Get source statement number
            statement_number = data.get('statement_number', 0)
            source_statement_number = data.get('source_statement_number', statement_number)

            excel_data = [{
                "Statement_ID": statement_number,
                "Source_Statement_Number": source_statement_number,
                "Attempt_Number": data.get('attempt_number', 1),
                "Timestamp": current_timestamp,
                "Total_Modules": data.get('total_modules', 0),
                "Pre_Count": data.get('pre_count', 0),
                "Responsible_Count": data.get('responsible_count', 0),
                "Post_Count": data.get('post_count', 0),
                "Overall_Status": data.get('overall_status', ''),
                "Final_Output": data.get('final_output', ''),
                "Pipeline_Phase": data.get('pipeline_phase', ''),
                "Success": data.get('success', False),
                "Notes": data.get('notes', '')
            }]

            append_sheet_to_excel(excel_path, "Pipeline_Overview", excel_data)
            print(f"📊 Logged pipeline overview to Pipeline_Overview sheet")

        except Exception as e:
            print(f"❌ Error logging pipeline overview to Excel: {str(e)}")

    def log_module_processing(self, excel_path: str, data: Dict[str, Any]) -> None:
        """Log detailed module processing to Excel."""
        try:
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            # Get source statement number
            statement_number = data.get('statement_number', 0)
            source_statement_number = data.get('source_statement_number', statement_number)

            excel_data = [{
                "Statement_ID": statement_number,
                "Source_Statement_Number": source_statement_number,
                "Attempt_Number": data.get('attempt_number', 1),
                "Operation": data.get('operation', ''),
                "Module_Name": data.get('module_name', ''),
                "Module_Type": data.get('module_type', ''),  # pre/responsible/post
                "Input_Code": data.get('input_code', ''),  # Full code, no truncation
                "Output_Code": data.get('output_code', ''),  # Full code, no truncation
                "Status": data.get('status', ''),
                "Changes_Made": data.get('changes_made', False),
                "Error_Details": data.get('error_details', ''),
                "File_Path": data.get('file_path', ''),
                "Module_Order": data.get('module_order', 0),
                "Timestamp": current_timestamp
            }]

            append_sheet_to_excel(excel_path, "Module_Processing", excel_data)
            print(f"📊 Logged {data.get('operation')} for {data.get('module_name', 'unknown')} to Module_Processing sheet")

        except Exception as e:
            print(f"❌ Error logging module processing to Excel: {str(e)}")

    def log_enhancement_feedback(self, excel_path: str, data: Dict[str, Any]) -> None:
        """Log enhancement feedback to Excel."""
        try:
            # Get source statement number
            statement_number = data.get('statement_number', 0)
            source_statement_number = data.get('source_statement_number', statement_number)

            excel_data = [{
                "Statement_ID": statement_number,
                "Source_Statement_Number": source_statement_number,
                "Attempt_Number": data.get('attempt_number', 1),
                "Feedback_Type": data.get('feedback_type', ''),  # validation/execution/comparison
                "Source_Node": data.get('source_node', ''),
                "Feedback_Content": data.get('feedback_content', ''),  # Full feedback, no truncation
                "Retry_Guidance": data.get('retry_guidance', ''),
                "Failed_Modules": data.get('failed_modules', ''),
                "Error_Details": data.get('error_details', ''),
                "Additional_Context": data.get('additional_context', ''),
                "Mismatch_Explanation": data.get('mismatch_explanation', ''),
                "Transformation_Required": data.get('transformation_required', ''),
                "Specific_Changes": data.get('specific_changes', ''),
                "Implementation_Steps": data.get('implementation_steps', ''),
                "Transformation_Summary": data.get('transformation_summary', '')
            }]

            append_sheet_to_excel(excel_path, "Enhancement_Feedback", excel_data)
            print(f"📊 Logged {data.get('feedback_type')} feedback to Enhancement_Feedback sheet")

        except Exception as e:
            print(f"❌ Error logging enhancement feedback to Excel: {str(e)}")

    def log_developer_summary(self, excel_path: str, data: Dict[str, Any]) -> None:
        """Log developer-friendly summary for easy understanding."""
        try:
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            # Get source statement number
            statement_number = data.get('statement_number', 0)
            source_statement_number = data.get('source_statement_number', statement_number)

            excel_data = [{
                "Statement_ID": statement_number,
                "Source_Statement_Number": source_statement_number,
                "Attempt_Number": data.get('attempt_number', 1),
                "Developer_Summary": data.get('developer_summary', ''),
                "What_Happened": data.get('what_happened', ''),
                "Input_Data": data.get('input_data', ''),
                "Output_Data": data.get('output_data', ''),
                "Success_Status": data.get('success_status', ''),
                "Issues_Found": data.get('issues_found', ''),
                "Next_Steps": data.get('next_steps', ''),
                "Technical_Details": data.get('technical_details', ''),
                "Timestamp": current_timestamp
            }]

            append_sheet_to_excel(excel_path, "Developer_Summary", excel_data)
            print(f"📊 Logged developer summary to Developer_Summary sheet")

        except Exception as e:
            print(f"❌ Error logging developer summary to Excel: {str(e)}")

    def decompose_enhanced_module(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 10: Decompose Enhanced Module.

        Purpose: Decompose enhanced driver module back into individual modules
        with functional change detection.
        """
        print("\n" + "="*80)
        print("📦 DECOMPOSE ENHANCED MODULE")
        print("="*80)

        try:
            # Get enhanced driver code and original modules
            enhanced_driver_code = getattr(state, 'enhanced_driver_code', '')
            module_categories = getattr(state, 'module_categories', {})
            responsible_modules = module_categories.get('responsible', [])

            if not enhanced_driver_code:
                return {"success": False, "error": "No enhanced driver code available"}

            # Extract individual modules using boundary markers
            decomposed_modules = []
            attempt_number = getattr(state, 'current_attempt', 1)

            print(f"🔍 Decomposing enhanced driver into {len(responsible_modules)} individual modules")

            for module_data in responsible_modules:
                # Handle different data structures
                if isinstance(module_data, tuple) and len(module_data) >= 2:
                    module_name = module_data[0]
                    original_path = module_data[1]
                elif isinstance(module_data, dict):
                    module_name = module_data.get('module_name', '')
                    original_path = module_data.get('module_path', '')
                else:
                    print(f"⚠️ Unexpected module data format: {module_data}")
                    continue
                # Extract module code between boundary markers
                start_marker = f"# === MODULE: {module_name} START ==="
                end_marker = f"# === MODULE: {module_name} END ==="

                enhanced_module_code = self.extract_module_from_combined(
                    enhanced_driver_code, start_marker, end_marker
                )

                if enhanced_module_code:
                    print(f"   📦 Extracted enhanced module: {module_name}")

                    # Read original module for comparison (to detect functional changes)
                    print(f"   🔍 Reading original module for change detection: {module_name}")
                    original_code = self.read_and_decrypt_module(module_name, original_path,
                                                               state.migration_name, state.cloud_category, "change_detection")

                    # Detect functional changes between original and enhanced
                    print(f"   ⚖️ Comparing original vs enhanced for functional changes: {module_name}")
                    has_functional_changes = self.detect_functional_changes(original_code, enhanced_module_code)

                    # Save individual enhanced module (pass original_code to avoid re-reading)
                    enhanced_module_path, module_id = self.save_decomposed_module(
                            module_name, enhanced_module_code, attempt_number, state, original_code
                        )
                    if has_functional_changes:
                        print(f"   ✅ {module_name}: Functional changes detected - saved enhanced module")
                    else:
                        print(f"   ⚪ {module_name}: No functional changes detected - enhanced module identical to original")

                    decomposed_modules.append({
                        'module_name': module_name,
                        'original_path': original_path,
                        'enhanced_path': enhanced_module_path,
                        'enhanced_code': enhanced_module_code,
                        'original_code': original_code,
                        'has_functional_changes': has_functional_changes,
                        'module_id': module_id  # Add module_id for database updates
                    })
                else:
                    print(f"⚠️ Could not extract module {module_name} from enhanced driver")

            # Log to Excel using new hierarchical system
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                modules_with_changes = sum(1 for m in decomposed_modules if m['has_functional_changes'])

                # Get module counts from state for consistent data
                module_categories = getattr(state, 'module_categories', {})
                pre_count = len(module_categories.get('pre_execution', []))
                responsible_count = len(module_categories.get('responsible', []))
                post_count = len(module_categories.get('post_execution', []))
                total_modules = pre_count + responsible_count + post_count

                # Get source statement number using dedicated function
                source_statement_number = self.get_current_source_statement_number(state)

                # Log pipeline overview update with correct data
                self.log_pipeline_overview(excel_path, {
                    'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                    'source_statement_number': source_statement_number,
                    'attempt_number': attempt_number,
                    'total_modules': total_modules,
                    'pre_count': pre_count,
                    'responsible_count': responsible_count,
                    'post_count': post_count,
                    'overall_status': 'MODULE_DECOMPOSITION_COMPLETE',
                    'pipeline_phase': 'Module Decomposition',
                    'success': True,
                    'notes': f"Decomposed {len(decomposed_modules)} modules, {modules_with_changes} with functional changes. Total pipeline: {pre_count} pre + {responsible_count} responsible + {post_count} post = {total_modules} modules"
                })

                # Log each decomposed module
                for i, module_data in enumerate(decomposed_modules):
                    self.log_module_processing(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': attempt_number,
                        'operation': 'MODULE_DECOMPOSITION',
                        'module_name': module_data['module_name'],
                        'module_type': 'responsible',
                        'input_code': 'Extracted from enhanced driver module',
                        'output_code': module_data['enhanced_code'],  # Full decomposed module code
                        'status': 'DECOMPOSED_WITH_CHANGES' if module_data['has_functional_changes'] else 'DECOMPOSED_NO_CHANGES',
                        'changes_made': module_data['has_functional_changes'],
                        'file_path': module_data.get('enhanced_module_path', ''),
                        'module_order': i + 1,
                        'additional_info': f"Original path: {module_data['original_path']}, Functional changes: {module_data['has_functional_changes']}"
                    })

                    # Small delay to prevent Excel file corruption
                    time.sleep(0.05)

            print(f"✅ Module decomposition completed")
            print(f"📊 {sum(1 for m in decomposed_modules if m['has_functional_changes'])}/{len(decomposed_modules)} modules have functional changes")

            return {
                "decomposed_modules": decomposed_modules,
                "modules_with_changes": sum(1 for m in decomposed_modules if m['has_functional_changes']),
                "decomposition_summary": f"Decomposed {len(decomposed_modules)} modules from driver",
                "success": True
            }

        except Exception as e:
            print(f"❌ Error in decompose_enhanced_module: {str(e)}")
            return {"success": False, "error": str(e)}

    def validate_module_enhancement(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 11: Validate Module Enhancement.

        Purpose: Validate decomposed modules for syntax, boundary markers, and functional changes.
        Provides feedback for enhancement retry if validation fails.
        """
        print("\n" + "="*80)
        print("✅ VALIDATE MODULE ENHANCEMENT")
        print("="*80)

        try:
            # Get decomposed modules
            decomposed_modules = getattr(state, 'decomposed_modules', [])

            if not decomposed_modules:
                print("⚠️ No decomposed modules available for validation")
                return {"success": False, "error": "No decomposed modules to validate"}

            print(f"🔍 Validating {len(decomposed_modules)} decomposed modules")

            validation_results = []
            overall_validation_passed = True

            print(f"🔍 Validating {len(decomposed_modules)} decomposed modules")

            for module_data in decomposed_modules:
                module_name = module_data['module_name']
                enhanced_code = module_data['enhanced_code']
                has_functional_changes = module_data['has_functional_changes']

                # Validation checks
                validation_result = {
                    'module_name': module_name,
                    'has_functional_changes': has_functional_changes,
                    'syntax_valid': True,
                    'has_boundary_markers': True,
                    'validation_passed': True,
                    'validation_errors': []
                }

                # 1. Syntax validation
                try:
                    compile(enhanced_code, f"<{module_name}>", "exec")
                    print(f"   ✅ {module_name}: Syntax validation passed")
                except SyntaxError as e:
                    validation_result['syntax_valid'] = False
                    validation_result['validation_errors'].append(f"Syntax error: {str(e)}")
                    print(f"   ❌ {module_name}: Syntax error - {str(e)}")

                # 2. Boundary marker validation
                start_marker = f"# === MODULE: {module_name} START ==="
                end_marker = f"# === MODULE: {module_name} END ==="

                if start_marker not in enhanced_code or end_marker not in enhanced_code:
                    validation_result['has_boundary_markers'] = False
                    validation_result['validation_errors'].append("Missing boundary markers")
                    print(f"   ❌ {module_name}: Missing boundary markers")
                else:
                    print(f"   ✅ {module_name}: Boundary markers present")

                # 3. Overall validation
                validation_result['validation_passed'] = (
                    validation_result['syntax_valid'] and
                    validation_result['has_boundary_markers']
                )

                if not validation_result['validation_passed']:
                    overall_validation_passed = False

                validation_results.append(validation_result)

            # Prepare validation feedback if failed
            if not overall_validation_passed:
                failed_modules = [r for r in validation_results if not r['validation_passed']]
                validation_feedback = {
                    'failed_modules': [r['module_name'] for r in failed_modules],
                    'validation_errors': [error for r in failed_modules for error in r['validation_errors']],
                    'retry_guidance': "Fix syntax errors and ensure boundary markers are preserved"
                }

                print(f"❌ Validation failed for {len(failed_modules)} modules")
                print(f"🔄 Preparing validation feedback for enhancement retry")
                print(f"📋 VALIDATION FEEDBACK DATA:")
                print(f"   ❌ Failed Modules: {validation_feedback['failed_modules']}")
                print(f"   🐛 Validation Errors: {validation_feedback['validation_errors']}")
                print(f"   🔧 Retry Guidance: {validation_feedback['retry_guidance']}")
                print(f"   📤 Feedback will be used for enhancement retry")

                # Log validation feedback to Excel
                excel_path = getattr(state, 'stage2_excel_path', None)
                if excel_path:
                    # Get source statement number using dedicated function
                    source_statement_number = self.get_current_source_statement_number(state)

                    self.log_enhancement_feedback(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'feedback_type': 'validation',
                        'source_node': 'validate_module_enhancement',
                        'feedback_content': str(validation_feedback),
                        'retry_guidance': validation_feedback['retry_guidance'],
                        'failed_modules': ', '.join(validation_feedback['failed_modules']),
                        'error_details': ', '.join(validation_feedback['validation_errors']),
                        'next_action': 'enhance_driver_module',
                        'priority': 'high'
                    })

            # Pipeline validation logging removed as not required

            print(f"✅ Module validation completed")
            print(f"📊 {sum(1 for r in validation_results if r['validation_passed'])}/{len(validation_results)} modules passed validation")

            # Prepare return data with validation feedback if failed
            return_data = {
                "validation_results": validation_results,
                "validation_passed": overall_validation_passed,
                "modules_validated": len(validation_results),
                "modules_passed": sum(1 for r in validation_results if r['validation_passed']),
                "success": True
            }

            # Add validation feedback to return data if validation failed
            if not overall_validation_passed:
                current_attempt = getattr(state, 'validation_attempt', 1)
                max_validation_attempts = 5

                if current_attempt > max_validation_attempts:
                    # Clear validation feedback when max attempts reached
                    print(f"🗑️ Clearing validation feedback (max attempts {max_validation_attempts} reached, proceeding to execution)")
                    return_data["validation_feedback"] = None
                else:
                    return_data["validation_feedback"] = validation_feedback
                    # Increment validation attempt counter for retry
                    return_data["validation_attempt"] = current_attempt + 1
                    # Clear execution feedback to prevent accumulation
                    if hasattr(state, 'execution_feedback') and state.execution_feedback:
                        print("🗑️ Clearing execution feedback (validation retry in progress)")
                        return_data["execution_feedback"] = None
            else:
                # Clear validation feedback when validation succeeds
                if hasattr(state, 'validation_feedback') and state.validation_feedback:
                    print("🗑️ Clearing validation feedback (validation retry succeeded)")
                    return_data["validation_feedback"] = None

            return return_data

        except Exception as e:
            print(f"❌ Error in validate_module_enhancement: {str(e)}")
            return {"success": False, "error": str(e)}

    def execute_complete_pipeline(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 12: Execute Complete Pipeline.

        Purpose: Execute complete pipeline (pre + enhanced responsible + post)
        and provide execution feedback if failures occur.
        """
        print("\n" + "="*80)
        print("⚡ EXECUTE COMPLETE PIPELINE")
        print("="*80)

        try:
            # Get pipeline components
            pre_processed_output = getattr(state, 'pre_processed_output', '')
            decomposed_modules = getattr(state, 'decomposed_modules', [])
            module_categories = getattr(state, 'module_categories', {})
            post_execution_modules = module_categories.get('post_execution', [])
            responsible_features = getattr(state, 'responsible_features', [])
            execution_source = getattr(state, 'execution_source', 'enhancement')

            print(f"🎯 Purpose: Execute complete pipeline with registry-aware module loading")
            print(f"📊 Execution Source: {execution_source}")
            print(f"📦 Responsible Modules: {len(responsible_features)}")
            print(f"🔧 Post Modules: {len(post_execution_modules)}")
            print(f"📝 Input Length: {len(pre_processed_output)} characters")

            if not pre_processed_output:
                return {"success": False, "error": "No pre-processed output available"}

            # Phase 1: Execute Responsible Modules (Registry-aware)
            current_output = pre_processed_output
            responsible_execution_log = []
            post_execution_log = []
            execution_success = True
            execution_errors = []

            # Get complete pipeline for natural order execution
            current_statement_index = getattr(state, 'current_statement_index', 0)
            available_features_with_statements = getattr(state, 'available_features_with_statements', [])
            current_statement = available_features_with_statements[current_statement_index]

            available_features = current_statement.get('available_features', [])
            post_features = current_statement.get('post_features', [])
            complete_pipeline = available_features + post_features

            # Create sets for efficient lookup
            pre_execution_modules = getattr(state, 'module_categories', {}).get('pre_execution', [])
            pre_execution_set = {(f[0], f[1]) for f in pre_execution_modules if isinstance(f, tuple) and len(f) >= 2}
            responsible_set = {(rf[0], rf[1]) for rf in responsible_features if isinstance(rf, tuple) and len(rf) >= 2}

            print(f"\n🔍 COMPLETE PIPELINE EXECUTION")
            print(f"   🔗 Complete pipeline: {[(f[0], f[1]) if isinstance(f, tuple) and len(f) >= 2 else str(f) for f in complete_pipeline]}")
            print(f"   📋 Pre-execution (skip): {list(pre_execution_set)}")
            print(f"   🎯 Responsible (enhanced): {list(responsible_set)}")
            print(f"   ⚡ Execution Type: {execution_source}")

            # Execute complete pipeline in natural order
            for module_tuple in complete_pipeline:
                if not isinstance(module_tuple, tuple) or len(module_tuple) < 2:
                    continue

                feature_name = module_tuple[0]
                module_path = module_tuple[1]
                current_module_tuple = (feature_name, module_path)

                # Skip pre-execution modules (already executed)
                if current_module_tuple in pre_execution_set:
                    print(f"   ⏭️ Skipping {feature_name} (already executed in pre-phase)")
                    continue

                # Execute responsible modules with enhanced logic
                elif current_module_tuple in responsible_set:
                    print(f"   🔧 Executing {feature_name} (responsible - enhanced)")
                    try:
                        previous_output = current_output
                        module_type = 'unknown'

                        # Execute based on execution source
                        if execution_source in ['registry', 'mixed'] and not decomposed_modules:
                            # Direct execution path - registry first, then original
                            if check_registry_for_module(module_path, state.migration_name, state.cloud_category):
                                print(f"      🗂️ Using registry module: {feature_name}")
                                enhanced_code = load_enhanced_module_from_registry(module_path, state.migration_name, state.cloud_category)
                                if enhanced_code:
                                    current_output = self.execute_enhanced_module(current_output, feature_name, enhanced_code, state.schema_name)
                                    module_type = 'enhanced_registry'
                                else:
                                    raise RuntimeError(f"Failed to load enhanced module from registry: {feature_name}")
                            else:
                                print(f"      📁 Using original module: {feature_name}")
                                original_code = self.read_and_decrypt_module(feature_name, module_path, state.migration_name, state.cloud_category, "direct_execution")
                                if original_code:
                                    current_output = self.apply_feature_module(current_output, feature_name, original_code, state)
                                    module_type = 'original'
                                else:
                                    raise RuntimeError(f"Failed to load original module: {feature_name}")

                        elif decomposed_modules:
                            # Enhancement execution path - decomposed first, then registry, then original
                            enhanced_executed = False
                            for module_data in decomposed_modules:
                                if (module_data.get('module_name') == feature_name and
                                    module_data.get('original_path') == module_path):
                                    print(f"      ✅ Using enhanced module: {feature_name}")
                                    enhanced_code = module_data['enhanced_code']
                                    current_output = self.execute_enhanced_module(current_output, feature_name, enhanced_code, state.schema_name)
                                    module_type = 'enhanced_decomposed'
                                    enhanced_executed = True
                                    break

                            if not enhanced_executed:
                                # Fallback to registry, then original
                                if check_registry_for_module(module_path, state.migration_name, state.cloud_category):
                                    print(f"      🗂️ Using registry module: {feature_name}")
                                    enhanced_code = load_enhanced_module_from_registry(module_path, state.migration_name, state.cloud_category)
                                    if enhanced_code:
                                        current_output = self.execute_enhanced_module(current_output, feature_name, enhanced_code, state.schema_name)
                                        module_type = 'enhanced_registry'
                                    else:
                                        raise RuntimeError(f"Failed to load enhanced module from registry: {feature_name}")
                                else:
                                    print(f"      📁 Using original module: {feature_name}")
                                    original_code = self.read_and_decrypt_module(feature_name, module_path, state.migration_name, state.cloud_category, "pipeline_execution")
                                    if original_code:
                                        current_output = self.apply_feature_module(current_output, feature_name, original_code, state)
                                        module_type = 'original'
                                    else:
                                        raise RuntimeError(f"Failed to load original module: {feature_name}")

                        transformation_applied = current_output != previous_output
                        responsible_execution_log.append({
                            'module_name': feature_name,
                            'module_type': module_type,
                            'transformation_applied': transformation_applied,
                            'input_length': len(previous_output),
                            'output_length': len(current_output),
                            'success': True
                        })

                        print(f"      ✅ {feature_name} ({module_type}): {'Applied transformation' if transformation_applied else 'No changes'}")

                    except Exception as e:
                        execution_success = False
                        error_msg = f"Responsible module {feature_name} execution failed: {str(e)}"
                        execution_errors.append(error_msg)
                        responsible_execution_log.append({
                            'module_name': feature_name,
                            'module_type': 'failed',
                            'transformation_applied': False,
                            'input_length': len(current_output),
                            'output_length': len(current_output),
                            'success': False,
                            'error': str(e)
                        })
                        print(f"      ❌ {feature_name}: Execution error - {str(e)}")

                # Execute post modules with registry/original priority
                else:
                    print(f"   📁 Executing {feature_name} (post - registry/original)")
                    try:
                        previous_output = current_output
                        module_type = 'unknown'

                        # Check registry first, then original
                        if check_registry_for_module(module_path, state.migration_name, state.cloud_category):
                            print(f"      🗂️ Using registry module: {feature_name}")
                            enhanced_code = load_enhanced_module_from_registry(module_path, state.migration_name, state.cloud_category)
                            if enhanced_code:
                                current_output = self.execute_enhanced_module(current_output, feature_name, enhanced_code, state.schema_name)
                                module_type = 'enhanced_registry'
                            else:
                                raise RuntimeError(f"Failed to load enhanced module from registry: {feature_name}")
                        else:
                            print(f"      📁 Using original module: {feature_name}")
                            original_code = self.read_and_decrypt_module(feature_name, module_path, state.migration_name, state.cloud_category, "pipeline_execution")
                            if original_code:
                                current_output = self.apply_feature_module(current_output, feature_name, original_code, state)
                                module_type = 'original'
                            else:
                                raise RuntimeError(f"Failed to load original module: {feature_name}")

                        transformation_applied = current_output != previous_output
                        post_execution_log.append({
                            'module_name': feature_name,
                            'module_type': module_type,
                            'transformation_applied': transformation_applied,
                            'input_length': len(previous_output),
                            'output_length': len(current_output)
                        })

                        print(f"      ✅ {feature_name} ({module_type}): {'Applied transformation' if transformation_applied else 'No changes'}")

                    except Exception as e:
                        execution_success = False
                        error_msg = f"Post module {feature_name} execution failed: {str(e)}"
                        execution_errors.append(error_msg)
                        print(f"      ❌ {feature_name}: Execution error - {str(e)}")

            responsible_output = current_output

            # Phase 2: Replace comment markers with original comments
            comments_dict = getattr(state, 'comments_dict', {})
            if comments_dict:
                print("🔄 Replacing comment markers with original comments...")
                current_output = replace_comment_markers(current_output, comments_dict)
                print(f"✅ Comment markers replaced successfully")
            else:
                print("ℹ️ No comments dictionary found, skipping comment replacement")

            final_output = current_output

            # Prepare execution feedback if failed
            if not execution_success:
                execution_feedback = {
                    'execution_errors': execution_errors,
                    'failed_phase': 'responsible' if any(any(keyword in err for keyword in ['Responsible module', 'Enhanced module', 'Original module']) for err in execution_errors) else 'post',
                    'retry_guidance': "Fix runtime errors in enhanced modules"
                }

                print(f"❌ Pipeline execution failed - preparing execution feedback")
                print(f"📋 EXECUTION FEEDBACK DATA:")
                print(f"   💥 Execution Errors: {execution_feedback['execution_errors']}")
                print(f"   📍 Failed Phase: {execution_feedback['failed_phase']}")
                print(f"   🔧 Retry Guidance: {execution_feedback['retry_guidance']}")
                print(f"   📤 Feedback will be used for enhancement retry")

                # Log execution feedback to Excel
                excel_path = getattr(state, 'stage2_excel_path', None)
                if excel_path:
                    # Get source statement number using dedicated function
                    source_statement_number = self.get_current_source_statement_number(state)

                    self.log_enhancement_feedback(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'feedback_type': 'execution',
                        'source_node': 'execute_complete_pipeline',
                        'feedback_content': str(execution_feedback),
                        'retry_guidance': execution_feedback['retry_guidance'],
                        'failed_modules': execution_feedback['failed_phase'],
                        'error_details': ', '.join(execution_feedback['execution_errors']),
                        'next_action': 'enhance_driver_module',
                        'priority': 'medium'
                    })

                print(f"❌ Pipeline execution failed with {len(execution_errors)} errors")
                print(f"🔄 Preparing execution feedback for enhancement retry")
            # Log to Excel
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                # Pipeline validation logging removed as not required

                # Get module counts from state for consistent data
                module_categories = getattr(state, 'module_categories', {})
                pre_count = len(module_categories.get('pre_execution', []))
                responsible_count = len(module_categories.get('responsible', []))
                post_count = len(module_categories.get('post_execution', []))
                total_modules = pre_count + responsible_count + post_count

                # Get source statement number using dedicated function
                source_statement_number = self.get_current_source_statement_number(state)

                # Get module information based on execution path
                if execution_source in ['registry', 'mixed']:
                    # Registry path - modules loaded directly from registry
                    responsible_features = getattr(state, 'responsible_features', [])
                    modules_executed = len(responsible_features)
                    execution_type = "registry"
                else:
                    # Enhancement path - modules from decomposition
                    modules_executed = len(decomposed_modules) if decomposed_modules else 0
                    execution_type = "enhanced"

                # Log pipeline overview update with correct data
                self.log_pipeline_overview(excel_path, {
                    'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                    'source_statement_number': source_statement_number,
                    'attempt_number': getattr(state, 'current_attempt', 1),
                    'total_modules': total_modules,
                    'pre_count': pre_count,
                    'responsible_count': responsible_count,
                    'post_count': post_count,
                    'overall_status': 'PIPELINE_EXECUTION_COMPLETE',
                    'pipeline_phase': 'Complete Pipeline Execution',
                    'success': execution_success,
                    'final_output': final_output,
                    'notes': f"Executed {modules_executed} {execution_type} modules + {len(post_execution_modules)} post modules. Status: {'SUCCESS' if execution_success else 'FAILED'}. Total pipeline: {pre_count} pre + {responsible_count} responsible + {post_count} post = {total_modules} modules"
                })

                # Log responsible modules execution to Excel (for all execution paths)
                for i, log_entry in enumerate(responsible_execution_log):
                    # Determine operation type based on execution source
                    if execution_source in ['registry', 'mixed']:
                        operation_type = f"{execution_source.upper()}_EXECUTION"
                    else:
                        operation_type = "ENHANCED_EXECUTION"

                    self.log_module_processing(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'operation': operation_type,
                        'module_name': log_entry['module_name'],
                        'module_type': 'responsible',
                        'input_code': f"Input length: {log_entry['input_length']} chars",
                        'output_code': f"Output length: {log_entry['output_length']} chars",
                        'status': 'EXECUTED',
                        'changes_made': log_entry['transformation_applied'],
                        'module_order': i + 1,
                        'additional_info': f"Module type: {log_entry['module_type']}, Transformation applied: {log_entry['transformation_applied']}"
                    })

                    # Small delay to prevent Excel file corruption
                    time.sleep(0.05)

                # Log post-execution modules to new hierarchical system
                for i, log_entry in enumerate(post_execution_log):
                    self.log_module_processing(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'operation': 'POST_EXECUTION',
                        'module_name': log_entry['module_name'],
                        'module_type': 'post',
                        'input_code': f"Input length: {log_entry['input_length']} chars",
                        'output_code': f"Output length: {log_entry['output_length']} chars",
                        'status': 'EXECUTED',
                        'changes_made': log_entry['transformation_applied'],
                        'module_order': i + 1,
                        'additional_info': f"Transformation applied: {log_entry['transformation_applied']}"
                    })

                    # Small delay to prevent Excel file corruption
                    time.sleep(0.05)

            print(f"✅ Complete pipeline execution completed")
            print(f"📤 Final output: {final_output}")
            print(f"📊 Execution status: {'SUCCESS' if execution_success else 'FAILED'}")

            # Get module information for pipeline summary based on execution path
            if execution_source in ['registry', 'mixed']:
                # Registry path - modules loaded directly from registry
                responsible_features = getattr(state, 'responsible_features', [])
                modules_executed = len(responsible_features)
                execution_type = "registry"
            else:
                # Enhancement path - modules from decomposition
                modules_executed = len(decomposed_modules) if decomposed_modules else 0
                execution_type = "enhanced"

            # Prepare return data with execution feedback if failed
            return_data = {
                "final_output": final_output,
                "responsible_output": responsible_output,
                "execution_success": execution_success,
                "execution_errors": execution_errors,
                "responsible_execution_log": responsible_execution_log,
                "post_execution_log": post_execution_log,
                "pipeline_summary": f"Executed {modules_executed} {execution_type} modules + {len(post_execution_modules or [])} post modules",
                "success": True
            }


            # Add execution feedback to return data if execution failed
            if not execution_success:
                current_attempt = getattr(state, 'execution_attempt', 1)
                max_execution_attempts = 5
                execution_source = getattr(state, 'execution_source', 'enhancement')

                # Set execution_source for routing logic
                if execution_source in ['registry', 'mixed']:
                    print(f"❌ Registry/mixed execution failed - routing to combine enhanced modules")
                    return_data["execution_source"] = f"{execution_source}_failed"
                else:
                    print(f"❌ Enhancement execution failed - routing to combine original modules")
                    # Keep execution_source as 'enhancement' for routing

                if current_attempt > max_execution_attempts:
                    # Clear execution feedback when max attempts reached
                    print(f"🗑️ Clearing execution feedback (max attempts {max_execution_attempts} reached, proceeding to AI comparison)")
                    return_data["execution_feedback"] = None
                else:
                    return_data["execution_feedback"] = execution_feedback
                    # Increment execution attempt counter for retry
                    return_data["execution_attempt"] = current_attempt + 1
                    # Clear validation feedback to prevent accumulation
                    if hasattr(state, 'validation_feedback') and state.validation_feedback:
                        print("🗑️ Clearing validation feedback (execution retry in progress)")
                        return_data["validation_feedback"] = None
            else:
                # Clear execution feedback when execution succeeds
                if hasattr(state, 'execution_feedback') and state.execution_feedback:
                    print("🗑️ Clearing execution feedback (execution retry succeeded)")
                    return_data["execution_feedback"] = None

            return return_data

        except Exception as e:
            print(f"❌ Error in execute_complete_pipeline: {str(e)}")
            return {"success": False, "error": str(e)}


    def ai_statement_comparison_pipeline(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 13: AI Statement Comparison Pipeline - Functional Equivalence Analysis

        Purpose:
            Performs sophisticated AI-driven comparison between the final pipeline output
            and the expected AI converted statement to determine functional equivalence.
            This is the final validation step that determines if enhancement was successful.

        Business Logic:
            1. Compares final_output (from execute_complete_pipeline) vs ai_converted_statement
            2. Uses AI to analyze functional equivalence beyond simple text matching
            3. Ignores formatting differences (whitespace, case, comments)
            4. Focuses on logical and functional equivalence
            5. Provides detailed comparison feedback for failed attempts

        AI Comparison Process:
            - Normalizes both statements for comparison (removes formatting differences)
            - Analyzes SQL logic, function calls, data transformations
            - Identifies functional differences vs cosmetic differences
            - Provides specific guidance for fixing functional discrepancies
            - Considers database-specific syntax variations

        Comparison Criteria:
            - Functional logic equivalence
            - Data transformation accuracy
            - SQL syntax correctness
            - Database function usage
            - Parameter handling
            - Error handling logic

        Input Requirements:
            - state.final_output: Final pipeline execution result
            - state.ai_converted_statement: Expected conversion target
            - state.original_source_statement: Original source for context
            - state.migration_name: For database-specific analysis
            - state.max_attempts: Maximum retry attempts allowed

        Output:
            - comparison_result: Boolean indicating functional equivalence
            - comparison_feedback: Detailed AI analysis of differences
            - statements_match: Simple boolean for workflow routing
            - comparison_details: Technical comparison breakdown

        Next Nodes:
            - Success (match) → more_statements_decision
            - Failure (no match) → enhancement_iteration_control (retry)

        Error Handling:
            - AI comparison failures with fallback analysis
            - Missing statement data validation
            - Comparison timeout handling
        """
        print("\n" + "="*80)
        print("🧠 AI STATEMENT COMPARISON PIPELINE")
        print("="*80)

        try:
            # Get comparison inputs
            final_output = getattr(state, 'final_output', '')
            execution_source = getattr(state, 'execution_source', 'enhancement')
            current_statement_index = getattr(state, 'current_statement_index', 0)
            available_features_with_statements = getattr(state, 'available_features_with_statements', [])
            current_statement = available_features_with_statements[current_statement_index]
            expected_output = current_statement.get('ai_converted_statement', '')

            # final_output = final_output.replace("BEGIN\nBEGIN", "BEGIN")

            print(f"🎯 Purpose: AI-driven functional equivalence analysis (final validation step)")
            print(f"📊 Execution Source: {execution_source}")
            print(f"📝 Statement: {current_statement_index + 1}/{len(available_features_with_statements)}")
            print(f"📏 Pipeline Output: {len(final_output)} characters")
            print(f"📏 Expected Output: {len(expected_output)} characters")

            if not final_output or not expected_output:
                error_msg = f"❌ Missing comparison inputs: final_output={bool(final_output)}, expected_output={bool(expected_output)}"
                print(error_msg)
                return {"success": False, "error": error_msg}

            # Prepare comparison data for ai_compare_statement_functionality
            statement_data = {
                'updated_ai_converted_statement': final_output,  # Our pipeline output
                'ai_converted_statement': expected_output        # Expected correct output from CSV
            }

            print(f"\n🔍 FUNCTIONAL EQUIVALENCE ANALYSIS:")
            print(f"   🎯 Comparing pipeline output vs expected AI output")
            print(f"   🚫 Comments will be removed for comparison (focus on functional logic)")
            print(f"   🧠 AI will analyze functional equivalence beyond text matching")
            print(f"🎯 EXPECTED OUTPUT ({len(expected_output)} chars):")
            print(f"{'='*80}")
            print(expected_output)
            print(f"{'='*80}")
            
            print(f"📝 PIPELINE OUTPUT ({len(final_output)} chars):")
            print(f"{'='*80}")
            print(final_output)
            print(f"{'='*80}")

            # Validate required data - fail immediately if missing
            if not final_output:
                error_msg = "❌ AI Comparison failed: final_output is empty - cannot compare statements"
                print(error_msg)
                return {
                    "statements_match": False,
                    "comparison_failed": True,
                    "error": error_msg,
                    "attempt_history": getattr(state, 'attempt_history', [])
                }

            if not expected_output:
                error_msg = "❌ AI Comparison failed: expected_output (ai_converted_statement) is empty - cannot compare statements"
                print(error_msg)
                return {
                    "statements_match": False,
                    "comparison_failed": True,
                    "error": error_msg,
                    "attempt_history": getattr(state, 'attempt_history', [])
                }

            # Get responsible modules context for module-aware comparison
            responsible_features = getattr(state, 'responsible_features', [])
            responsible_modules_context = []
            for feature_tuple in responsible_features:
                if len(feature_tuple) >= 3:
                    module_info = {
                        'module_name': feature_tuple[0],
                        'module_path': feature_tuple[1],
                        'responsibility_reason': feature_tuple[2]
                    }
                    responsible_modules_context.append(module_info)

            # AI Comparison using existing logic with module context
            print(f"🧠 AI comparison with {len(responsible_modules_context)} module(s) context: {[m['module_name'] for m in responsible_modules_context]}")
            comparison_result = self.ai_compare_statement_functionality(statement_data, state.migration_name, responsible_modules_context)

            statements_match = comparison_result.get('statements_match', False)
            explanation = comparison_result.get('explanation', '')
            transformation_guidance = comparison_result.get('transformation_guidance', {})

            # Prepare comparison feedback if failed
            if not statements_match:
                comparison_feedback = {
                    'mismatch_explanation': explanation,
                    'final_output': final_output,
                    'expected_output': expected_output,
                    'retry_guidance': "Address functional differences between pipeline output and expected output",
                    'transformation_guidance': transformation_guidance
                }

                print(f"❌ AI comparison failed - statements don't match")
                print(f"🔄 Preparing comparison feedback for enhancement retry")
                print(f"📋 AI COMPARISON FEEDBACK DATA:")
                print(f"   🔍 Mismatch Explanation: {comparison_feedback['mismatch_explanation']}")
                print(f"   📝 Pipeline Output: {comparison_feedback['final_output']}")
                print(f"   🎯 Expected Output: {comparison_feedback['expected_output']}")
                print(f"   🔧 Retry Guidance: {comparison_feedback['retry_guidance']}")

                # Show transformation guidance if available
                if transformation_guidance and transformation_guidance.get('required'):
                    print(f"   🛠️ Transformation Guidance Available:")
                    specific_changes = transformation_guidance.get('specific_changes', [])
                    for i, change in enumerate(specific_changes, 1):
                        print(f"      {i}. {change.get('source_pattern', '')} → {change.get('target_pattern', '')}")
                    implementation_steps = transformation_guidance.get('implementation_steps', [])
                    if implementation_steps:
                        print(f"   📋 Implementation Steps: {len(implementation_steps)} steps provided")

                print(f"   📤 Feedback will be used for enhancement retry")

                # Save failed attempt to memory for learning
                current_attempt = getattr(state, 'current_attempt', 1)
                updated_modules = getattr(state, 'updated_modules', [])

                if updated_modules:
                    print(f"💾 Saving failed attempt {current_attempt} to memory with {len(updated_modules)} modules")
                else:
                    print(f"⚠️ No updated modules found in state for attempt {current_attempt}")
                    # Try to get from recent node outputs
                    enhanced_driver_code = getattr(state, 'enhanced_driver_code', None)
                    decomposed_modules = getattr(state, 'decomposed_modules', [])
                    if enhanced_driver_code:
                        updated_modules = [{
                            'module_name': 'enhanced_driver_module',
                            'module_type': 'driver',
                            'code_content': enhanced_driver_code,
                            'attempt_number': current_attempt
                        }]
                    elif decomposed_modules:
                        updated_modules = []
                        for module_data in decomposed_modules:
                            updated_modules.append({
                                'module_name': module_data.get('module_name', 'decomposed_module'),
                                'module_type': 'responsible',
                                'code_content': module_data.get('enhanced_code', ''),
                                'attempt_number': current_attempt
                            })

                attempt_save_result = {}
                if updated_modules:
                    attempt_save_result = self.save_attempt_to_memory(
                        state,
                        current_attempt,
                        updated_modules,
                        explanation,  # AI comparison feedback
                        final_output  # Final output from pipeline
                    )
                    print(f"✅ Attempt {current_attempt} saved to memory")
                else:
                    print(f"⚠️ No updated modules found to save for attempt {current_attempt}")

            # Log to Excel with comprehensive developer information
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                # Get module counts from state for consistent data
                module_categories = getattr(state, 'module_categories', {})
                pre_count = len(module_categories.get('pre_execution', []))
                responsible_count = len(module_categories.get('responsible', []))
                post_count = len(module_categories.get('post_execution', []))
                total_modules = pre_count + responsible_count + post_count

                # Get source statement number using dedicated function
                source_statement_number = self.get_current_source_statement_number(state)

                # Get post features data from current statement
                current_statement_data = available_features_with_statements[current_statement_index]
                post_features = current_statement_data.get('post_features', [])
                post_features_str = ', '.join([f[0] if isinstance(f, tuple) else str(f) for f in post_features[:3]])
                if len(post_features) > 3:
                    post_features_str += f" and {len(post_features)-3} more"

                # Log to pipeline overview with complete data
                self.log_pipeline_overview(excel_path, {
                    'statement_number': current_statement_index + 1,
                    'source_statement_number': source_statement_number,
                    'attempt_number': getattr(state, 'current_attempt', 1),
                    'total_modules': total_modules,
                    'pre_count': pre_count,
                    'responsible_count': responsible_count,
                    'post_count': post_count,
                    'overall_status': 'AI_COMPARISON_COMPLETE',
                    'pipeline_phase': 'AI Statement Comparison',
                    'success': statements_match,
                    'final_output': final_output,
                    'notes': f"Comparison result: {'MATCH' if statements_match else 'NO MATCH'} - {explanation} | Execution source: {execution_source} | Post features: {post_features_str} | Total pipeline: {pre_count} pre + {responsible_count} responsible + {post_count} post = {total_modules} modules"
                })

                # Small delay to prevent Excel file corruption
                time.sleep(0.1)

                # Log detailed comparison for developers
                self.log_developer_summary(excel_path, {
                    'statement_number': current_statement_index + 1,
                    'source_statement_number': source_statement_number,
                    'attempt_number': getattr(state, 'current_attempt', 1),
                    'developer_summary': f"AI Statement Comparison: {'SUCCESS' if statements_match else 'FAILED'}",
                    'what_happened': f"Compared final pipeline output against expected AI converted statement. Result: {'MATCH' if statements_match else 'NO MATCH'}",
                    'input_data': f"Pipeline Output: {final_output}",
                    'output_data': f"Expected Output: {expected_output}",
                    'success_status': 'SUCCESS' if statements_match else 'FAILED',
                    'issues_found': explanation if not statements_match else 'No issues - statements match functionally',
                    'next_steps': 'Proceed to next statement' if statements_match else 'Retry enhancement with comparison feedback',
                    'technical_details': f"AI Analysis: {explanation}"
                })

                # Log comparison feedback if failed
                if not statements_match:
                    # Extract transformation guidance details for enhanced logging
                    transformation_guidance = comparison_feedback.get('transformation_guidance', {})
                    specific_changes = transformation_guidance.get('specific_changes', [])
                    implementation_steps = transformation_guidance.get('implementation_steps', [])

                    # Format specific changes for Excel
                    changes_text = ""
                    if specific_changes:
                        changes_list = []
                        for change in specific_changes:
                            source_pattern = change.get('source_pattern', '')
                            target_pattern = change.get('target_pattern', '')
                            changes_list.append(f"{source_pattern} → {target_pattern}")
                        changes_text = "; ".join(changes_list)

                    # Format implementation steps for Excel
                    steps_text = ""
                    if implementation_steps:
                        steps_text = "; ".join([f"{i+1}. {step}" for i, step in enumerate(implementation_steps)])

                    # Small delay to prevent Excel file corruption
                    time.sleep(0.1)

                    # Format transformation guidance for Excel logging (same as debug logging)
                    formatted_feedback_content = ""
                    if transformation_guidance and transformation_guidance.get('required'):
                        formatted_feedback_content = "🛠️ MODULE-SPECIFIC TRANSFORMATION GUIDANCE:\n"
                        specific_changes = transformation_guidance.get('specific_changes', [])
                        for i, change in enumerate(specific_changes, 1):
                            formatted_feedback_content += f"   {i}. Source Pattern: {change.get('source_pattern', '')}\n"
                            formatted_feedback_content += f"      Target Pattern: {change.get('target_pattern', '')}\n"
                            formatted_feedback_content += f"      Module Guidance: {change.get('transformation_method', '')}\n"
                            formatted_feedback_content += f"      Variations: {change.get('variations_to_handle', [])}\n"

                        implementation_steps = transformation_guidance.get('implementation_steps', [])
                        if implementation_steps:
                            formatted_feedback_content += "📋 Implementation Steps:\n"
                            for i, step in enumerate(implementation_steps, 1):
                                formatted_feedback_content += f"   {i}. {step}\n"

                        parameter_mapping = transformation_guidance.get('parameter_mapping', {})
                        if parameter_mapping:
                            formatted_feedback_content += "🔧 Parameter Mapping:\n"
                            for param_key, param_value in parameter_mapping.items():
                                formatted_feedback_content += f"   {param_key}: {param_value}\n"
                    else:
                        # Fallback to string representation if no transformation guidance
                        formatted_feedback_content = str(comparison_feedback)

                    # Log enhanced feedback with transformation guidance details
                    self.log_enhancement_feedback(excel_path, {
                        'statement_number': current_statement_index + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'feedback_type': 'comparison',
                        'source_node': 'ai_statement_comparison_pipeline',
                        'feedback_content': formatted_feedback_content,
                        'retry_guidance': comparison_feedback.get('retry_guidance', ''),
                        'error_details': explanation,
                        'next_action': 'enhance_driver_module',
                        'priority': 'low',
                        'additional_context': f"Pipeline: {final_output} | Expected: {expected_output}",
                        'mismatch_explanation': comparison_feedback.get('mismatch_explanation', ''),
                        'transformation_required': transformation_guidance.get('required', False),
                        'specific_changes': changes_text,
                        'implementation_steps': steps_text,
                        'transformation_summary': transformation_guidance.get('summary', '')
                    })

            print(f"✅ AI statement comparison completed")
            print(f"📊 Comparison result: {'MATCH' if statements_match else 'NO MATCH'}")
            if not statements_match:
                print(f"📝 Explanation: {explanation}")

            # Save attempt result to database
            current_statement_id = getattr(state, 'current_statement_id', None)
            current_attempt = getattr(state, 'current_attempt', 1)

            if current_statement_id:
                ai_comparison_status = 'Success' if statements_match else 'Failed'
                attempt_id = insert_update_statement_attempt(
                    db_data=get_qbook_db_config(),
                    statement_id=current_statement_id,
                    attempt_number=current_attempt,
                    stage2_output_statement=final_output,
                    ai_comparison_status=ai_comparison_status
                )
                print(f"✅ Attempt {current_attempt} result saved - ID: {attempt_id}, Status: {ai_comparison_status}")


                # Note: Cleanup is now handled at the beginning of each statement processing
                # in identify_responsible_features function for a clean slate approach

            else:
                print(f"⚠️ No statement_id found - attempt result not saved to database")

            # Prepare return data with comparison feedback if failed
            return_data = {
                "statements_match": statements_match,
                "comparison_result": comparison_result,
                "explanation": explanation,
                "final_output": final_output,
                "expected_output": expected_output,
                "success": True
            }

            # Add comparison feedback to return data if comparison failed
            if not statements_match:
                return_data["ai_comparison_feedback"] = comparison_feedback
                # Add attempt save result if it exists (defined in the comparison failure block)
                if 'attempt_save_result' in locals() and attempt_save_result:
                    return_data.update(attempt_save_result)

            return return_data

        except Exception as e:
            print(f"❌ Error in ai_statement_comparison_pipeline: {str(e)}")
            return {"success": False, "error": str(e)}

    def enhancement_iteration_control(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 14: Enhancement Iteration Control.

        Purpose: Control iteration logic based on AI comparison results and attempt limits.
        Manages proceed/retry/fail decisions with attempt tracking.
        """
        print("\n" + "="*80)
        print("🔄 ENHANCEMENT ITERATION CONTROL")
        print("="*80)

        try:
            # Get iteration control inputs
            statements_match = getattr(state, 'statements_match', False)
            current_attempt = getattr(state, 'current_attempt', 1)
            max_attempts = getattr(state, 'max_attempts', 5)

            print(f"🔍 Iteration control analysis:")
            print(f"   📊 Statements match: {statements_match}")
            print(f"   🔢 Current attempt: {current_attempt}/{max_attempts}")

            # Decision Logic
            if statements_match:
                # SUCCESS - Proceed to next statement
                iteration_action = "proceed"
                message = f'Statement enhancement successful after {current_attempt} attempts'

                print(f"✅ {message}")

                # Log success to Excel
                excel_path = getattr(state, 'stage2_excel_path', None)
                if excel_path:
                    # Get source statement number using dedicated function
                    source_statement_number = self.get_current_source_statement_number(state)

                    self.log_ai_analysis_control_to_excel(excel_path, {
                        'operation': 'ITERATION_CONTROL',
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': current_attempt,
                        'iteration_action': iteration_action,
                        'decision_reason': 'Statements match - enhancement successful',
                        'message': message
                    })

                return {
                    "iteration_action": iteration_action,
                    "success": True,
                    "message": message
                }

            elif current_attempt >= max_attempts:
                # FAILURE - Max attempts reached
                iteration_action = "fail"
                message = f'Statement enhancement failed after {max_attempts} attempts'

                print(f"❌ {message}")

                # Log failure to Excel
                excel_path = getattr(state, 'stage2_excel_path', None)
                if excel_path:
                    # Get source statement number using dedicated function
                    source_statement_number = self.get_current_source_statement_number(state)

                    self.log_ai_analysis_control_to_excel(excel_path, {
                        'operation': 'ITERATION_CONTROL',
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': current_attempt,
                        'iteration_action': iteration_action,
                        'decision_reason': 'Max attempts reached - marking as failed',
                        'message': message
                    })

                return {
                    "iteration_action": iteration_action,
                    "success": False,
                    "message": message,
                    "final_attempt_number": current_attempt
                }

            else:
                # RETRY - Increment attempt and retry enhancement
                next_attempt = current_attempt + 1
                iteration_action = "retry"
                message = f'Retrying enhancement - attempt {next_attempt}/{max_attempts}'

                print(f"🔄 {message}")

                # Log retry to Excel
                excel_path = getattr(state, 'stage2_excel_path', None)
                if excel_path:
                    # Get source statement number using dedicated function
                    source_statement_number = self.get_current_source_statement_number(state)

                    self.log_ai_analysis_control_to_excel(excel_path, {
                        'operation': 'ITERATION_CONTROL',
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': current_attempt,
                        'iteration_action': iteration_action,
                        'decision_reason': f'Statements do not match - retrying attempt {next_attempt}',
                        'message': message,
                        'next_attempt_number': next_attempt
                    })

                return {
                    "iteration_action": iteration_action,
                    "success": False,
                    "next_attempt_number": next_attempt,
                    "message": message,
                    "current_attempt": next_attempt  # Update attempt number
                }

        except Exception as e:
            print(f"❌ Error in enhancement_iteration_control: {str(e)}")
            return {"success": False, "error": str(e)}

    # ==================== ADDITIONAL HELPER FUNCTIONS ====================

    def extract_module_from_combined(self, combined_code: str, start_marker: str, end_marker: str) -> str:
        """Extract individual module code from combined driver module using boundary markers."""
        try:
            start_index = combined_code.find(start_marker)
            end_index = combined_code.find(end_marker)

            if start_index == -1 or end_index == -1:
                return ""

            # Extract code including markers for validation
            end_index += len(end_marker)
            module_code = combined_code[start_index:end_index].strip()

            return module_code

        except Exception as e:
            print(f"❌ Error extracting module from combined code: {str(e)}")
            return ""

    def remove_boundary_markers(self, module_code: str, module_name: str) -> str:
        """Remove boundary markers from module code to get clean code."""
        try:
            start_marker = f"# === MODULE: {module_name} START ==="
            end_marker = f"# === MODULE: {module_name} END ==="

            # Remove start marker
            if start_marker in module_code:
                module_code = module_code.replace(start_marker, "").strip()

            # Remove end marker
            if end_marker in module_code:
                module_code = module_code.replace(end_marker, "").strip()

            return module_code

        except Exception as e:
            print(f"❌ Error removing boundary markers: {str(e)}")
            return module_code  # Return original code if error

    def detect_functional_changes(self, original_code: str, enhanced_code: str) -> bool:
        """Detect if there are functional changes between original and enhanced code."""
        try:
            # Normalize code for comparison (remove comments, whitespace differences)
            original_normalized = self.normalize_code_for_comparison(original_code)
            enhanced_normalized = self.normalize_code_for_comparison(enhanced_code)

            return original_normalized != enhanced_normalized

        except Exception as e:
            print(f"❌ Error detecting functional changes: {str(e)}")
            return True  # Assume changes on error

    def normalize_code_for_comparison(self, code: str) -> str:
        """Normalize code by removing comments and normalizing whitespace."""
        try:
            lines = []
            for line in code.split('\n'):
                # Remove comments and strip whitespace
                line = line.split('#')[0].strip()
                if line:  # Only keep non-empty lines
                    lines.append(line)

            return '\n'.join(lines)

        except Exception as e:
            print(f"❌ Error normalizing code: {str(e)}")
            return code


    def save_decomposed_module(self, module_name: str, module_code: str, attempt_number: int, state: Stage2WorkflowState, original_code: str = None) -> str:
        """Save individual decomposed module (only if functionally changed)."""
        try:
            # Get source statement number for current statement
            source_statement_number = self.get_current_source_statement_number(state)

            # Get feature modules directory using source statement number
            paths_info = self.setup_module_update_paths(
                state,
                source_statement_number,
                attempt_number
            )
            feature_modules_dir = paths_info['feature_modules_dir']

            # Create directory if needed
            os.makedirs(feature_modules_dir, exist_ok=True)

            # Remove boundary markers from module code before saving
            clean_module_code = self.remove_boundary_markers(module_code, module_name)

            # Save decomposed module with attempt-based naming
            module_filename = f"{module_name}_attempt_{attempt_number}.py"
            module_path = os.path.join(feature_modules_dir, module_filename)

            with open(module_path, 'w', encoding='utf-8') as file:
                file.write(clean_module_code)

            print(f"   💾 Saved decomposed module: {module_path}")

            # Save module enhancement to database
            try:
                current_statement_id = getattr(state, 'current_statement_id', None)
                if current_statement_id:
                    # Use passed original_code if available, otherwise get it (for backward compatibility)
                    if original_code is not None:
                        original_module_code = original_code
                        print(f"   📋 Using pre-loaded original code for database save (avoiding duplicate read)")
                    else:
                        print(f"   📖 Reading original module code for database save (fallback)")
                        original_module_code = self.get_original_module_code(module_name, state)

                    original_module_path = self.get_original_module_path(module_name, state)  # e.g., Common/Statement/Pre/join.py

                    # Convert absolute path to relative path for updated_object_path (Stage1_Metadata path)
                    try:
                        # Get relative path from the full module_path for Stage1_Metadata structure
                        if state.cloud_category.lower() == 'local':
                            base_path = Config.Qbook_Local_Path
                        else:
                            base_path = Config.Qbook_Path
                        updated_relative_path = os.path.relpath(module_path, base_path)  # e.g., Stage1_Metadata/Oracle_Postgres14/HR/Procedure/EMPLOYEE_PROC/feature_modules/1/join_attempt_1.py
                    except:
                        updated_relative_path = module_path  # Fallback to full path

                    # Get registry module code if available
                    registry_module_code = None
                    if original_module_path:
                        # Check if enhanced version exists in registry
                        if check_registry_for_module(original_module_path, state.migration_name, state.cloud_category):
                            registry_module_code = load_enhanced_module_from_registry(original_module_path, state.migration_name, state.cloud_category)
                            print(f"   📋 Loaded registry module code for database save: {module_name}")

                    # Insert module enhancement record
                    module_id = insert_stage2_conversion_module(
                        db_data=get_qbook_db_config(),
                        statement_id=current_statement_id,
                        feature_name=module_name,
                        original_object_path=original_module_path,  # Original QMigrator module path (Common/Statement/Pre/join.py)
                        updated_object_path=updated_relative_path,  # Enhanced decomposed module path (Stage1_Metadata/...)
                        attempt=attempt_number,
                        is_inregistry=bool(registry_module_code),  # True if registry module exists
                        registry_path=self.get_relative_registry_path(original_module_path, state.migration_name, state.cloud_category) if registry_module_code else "",
                        original_module=original_module_code,
                        updated_module="",  # Empty initially for manual editing
                        registry_module_code=registry_module_code,  # Enhanced module code from registry
                        ai_module_code=clean_module_code  # AI-generated enhanced module code
                    )

                    if module_id:
                        print(f"✅ Saved module enhancement to database with ID: {module_id}")
                    else:
                        print("⚠️ Failed to save module enhancement to database")
                else:
                    print("⚠️ No current_statement_id available for module tracking")

            except Exception as db_error:
                print(f"⚠️ Database module insertion error: {str(db_error)}")
                # Continue processing even if database insertion fails

            return module_path, module_id

        except Exception as e:
            print(f"❌ Error saving decomposed module: {str(e)}")
            return "", None

    def get_original_module_code(self, module_name: str, state: Stage2WorkflowState) -> str:
        """Get original module code for comparison."""
        try:
            # Get available features to find the module path
            current_statement_index = getattr(state, 'current_statement_index', 0)
            available_features_with_statements = getattr(state, 'available_features_with_statements', [])

            if available_features_with_statements and current_statement_index < len(available_features_with_statements):
                current_statement = available_features_with_statements[current_statement_index]
                available_features = current_statement.get('available_features', [])

                # First, try to find in available_features
                for feature_name, relative_path in available_features:
                    if feature_name.lower() == module_name.lower():
                        return self.get_module_from_path(relative_path, module_name, state)

                # If not found in available_features, check responsible_features
                responsible_features = getattr(state, 'responsible_features', [])
                for feature_data in responsible_features:
                    if isinstance(feature_data, tuple) and len(feature_data) >= 2:
                        feature_name, relative_path = feature_data[0], feature_data[1]
                        if feature_name.lower() == module_name.lower():
                            return self.get_module_from_path(relative_path, module_name, state)

            return ""  # Return empty string if module not found

        except Exception as e:
            print(f"⚠️ Error getting original module code for {module_name}: {str(e)}")
            return ""

    def get_relative_registry_path(self, original_module_path: str, migration_name: str, cloud_category: str) -> str:
        """
        Get relative registry path for database storage (from Stage1_Metadata onwards).

        Args:
            original_module_path: Original module path (e.g., Common/Statement/Pre/join.py)
            migration_name: Migration name
            cloud_category: Cloud category (local/cloud)

        Returns:
            Relative registry path (e.g., Stage1_Metadata/Oracle_Postgres14/Enhanced_Modules_Registry/Common/Statement/Pre/join.py)
        """
        # Get absolute registry path
        registry_absolute_path = get_registry_path(original_module_path, migration_name, cloud_category)

        # Convert to relative path
        if cloud_category.lower() == 'local':
            base_path = Config.Qbook_Local_Path
        else:
            base_path = Config.Qbook_Path

        registry_relative_path = os.path.relpath(registry_absolute_path, base_path)
        return registry_relative_path.replace("\\", "/")

    def get_original_module_path(self, module_name: str, state: Stage2WorkflowState) -> str:
        """Get original module relative path."""
        try:
            # Get available features to find the module path
            current_statement_index = getattr(state, 'current_statement_index', 0)
            available_features_with_statements = getattr(state, 'available_features_with_statements', [])

            if available_features_with_statements and current_statement_index < len(available_features_with_statements):
                current_statement = available_features_with_statements[current_statement_index]
                available_features = current_statement.get('available_features', [])

                # First, try to find in available_features
                for feature_name, relative_path in available_features:
                    if feature_name.lower() == module_name.lower():
                        return relative_path

                # If not found in available_features, check responsible_features
                responsible_features = getattr(state, 'responsible_features', [])
                for feature_data in responsible_features:
                    if isinstance(feature_data, tuple) and len(feature_data) >= 2:
                        feature_name, relative_path = feature_data[0], feature_data[1]
                        if feature_name.lower() == module_name.lower():
                            return relative_path

            return ""  # Return empty string if module not found

        except Exception as e:
            print(f"⚠️ Error getting original module path for {module_name}: {str(e)}")
            return ""

    def get_module_from_path(self, relative_path: str, module_name: str, state: Stage2WorkflowState) -> str:
        """Helper method to get module code from relative path."""
        try:
            # Get full module path
            cloud_category = state.cloud_category
            migration_name = state.migration_name

            if cloud_category.lower() == 'local':
                qbook_path = Config.Qbook_Local_Path
            else:
                qbook_path = Config.Qbook_Path

            full_path = os.path.join(qbook_path, "Conversion_Modules", migration_name, relative_path)

            # Read and decrypt original module
            return self.read_and_decrypt_module(module_name, full_path, migration_name, cloud_category, "original_module_retrieval")

        except Exception as e:
            print(f"⚠️ Error getting module from path {relative_path}: {str(e)}")
            return ""



    def execute_enhanced_module(self, statement: str, module_name: str, module_code: str, schema_name: str) -> str:
        """Execute enhanced module code using importlib.util."""
        try:
            # Use existing module execution logic
            return self.apply_feature_module(statement, module_name, module_code,
                                           type('State', (), {'schema_name': schema_name})())

        except Exception as e:
            print(f"❌ Error executing enhanced module: {str(e)}")
            # Propagate the error instead of returning original statement
            # This allows execute_complete_pipeline to catch it and trigger enhancement retry
            raise



    def log_ai_analysis_control_to_excel(self, excel_path: str, data: Dict[str, Any]) -> None:
        """Log AI analysis and control results (comparison + iteration) to Excel."""
        try:
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            # Get source statement number
            statement_number = data.get('statement_number', 0)
            source_statement_number = data.get('source_statement_number', statement_number)

            excel_data = [{
                "Statement_ID": statement_number,
                "Source_Statement_Number": source_statement_number,
                "Attempt_Number": data.get('attempt_number', 1),
                "Operation": data.get('operation', ''),
                "Result": data.get('statements_match', data.get('iteration_action', '')),
                "Decision_Reason": data.get('decision_reason', data.get('explanation', '')),
                "Final_Output": data.get('final_output', ''),
                "Expected_Output": data.get('expected_output', ''),
                "Next_Attempt": data.get('next_attempt_number', ''),
                "Message": data.get('message', ''),
                "Feedback": data.get('decision_reason', ''),
                "Timestamp": current_timestamp
            }]

            append_sheet_to_excel(excel_path, "AI_Analysis_Control", excel_data)
            print(f"📊 Logged {data.get('operation')} to AI_Analysis_Control sheet")

        except Exception as e:
            print(f"❌ Error logging AI analysis control to Excel: {str(e)}")

    # ==================== REGISTRY INTEGRATION NODES ====================

    def enhanced_modules_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Decision node to route based on enhanced modules availability.

        Purpose:
            Determines whether to skip enhancement pipeline (if enhanced modules available)
            or proceed to standard enhancement pipeline (if only original modules available).

        Input Requirements:
            - state.enhanced_modules_loaded: Boolean indicating if enhanced modules were loaded
            - state.registry_status: Status from registry check

        Output:
            - execution_source: Set to "registry", "mixed", or "enhancement"
            - routing decision for graph builder

        Next Nodes:
            - Enhanced available → execute_complete_pipeline (skip enhancement)
            - Original only → combine_driver_module (standard enhancement)
        """
        try:
            enhanced_modules_loaded = getattr(state, 'enhanced_modules_loaded', False)
            registry_status = getattr(state, 'registry_status', 'none_in_registry')

            print(f"🔀 Enhanced Modules Decision:")
            print(f"   - Enhanced modules loaded: {enhanced_modules_loaded}")
            print(f"   - Registry status: {registry_status}")

            if enhanced_modules_loaded:
                execution_source = "registry" if registry_status == "all_in_registry" else "mixed"
                print(f"✅ Enhanced modules available - skipping enhancement pipeline")
                print(f"⚡ Execution source: {execution_source}")

                # No need to prepare decomposed_modules here
                # execute_complete_pipeline will handle registry-aware loading
                responsible_features = getattr(state, 'responsible_features', [])
                print(f"✅ Enhanced modules available - using registry-aware execution")
                print(f"   - Responsible modules: {len(responsible_features)} (registry check per module)")
                print(f"   - Post modules: Always original")

                # Excel logging following existing patterns
                excel_path = getattr(state, 'stage2_excel_path', None)
                if excel_path:
                    source_statement_number = self.get_current_source_statement_number(state)

                    module_categories = getattr(state, 'module_categories', {})
                    post_execution_modules = module_categories.get('post_execution', [])

                    self.log_pipeline_overview(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'total_modules': len(responsible_features) + len(post_execution_modules),
                        'pre_count': 0,
                        'responsible_count': len(responsible_features),
                        'post_count': len(post_execution_modules),
                        'overall_status': 'REGISTRY_AWARE_EXECUTION',
                        'pipeline_phase': 'Registry Decision',
                        'success': True,
                        'notes': f"Using registry-aware execution ({registry_status}). Responsible modules: registry check per module. Post modules: always original. Skipping enhancement pipeline for 90% performance gain."
                    })

                return {
                    "enhanced_modules_loaded": True,
                    "execution_source": execution_source,
                    "skip_enhancement": True
                }
            else:
                print("📁 Original modules only - proceeding to enhancement pipeline")

                # Excel logging for original modules path
                excel_path = getattr(state, 'stage2_excel_path', None)
                if excel_path:
                    source_statement_number = self.get_current_source_statement_number(state)

                    self.log_pipeline_overview(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'total_modules': len(getattr(state, 'missing_modules', [])),
                        'pre_count': 0,
                        'responsible_count': len(getattr(state, 'missing_modules', [])),
                        'post_count': 0,
                        'overall_status': 'ORIGINAL_MODULES_ONLY',
                        'pipeline_phase': 'Registry Decision',
                        'success': True,
                        'notes': f"No enhanced modules in registry. Proceeding to standard enhancement pipeline."
                    })

                return {
                    "enhanced_modules_loaded": False,
                    "execution_source": "enhancement",
                    "skip_enhancement": False
                }

        except Exception as e:
            print(f"❌ Error in enhanced modules decision: {str(e)}")
            # Default to enhancement pipeline on error
            return {
                "enhanced_modules_loaded": False,
                "execution_source": "enhancement",
                "skip_enhancement": False,
                "error": str(e)
            }

    def save_enhanced_to_registry(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Save enhanced modules to registry for future reuse.

        Purpose:
            Saves successfully enhanced modules to registry so they can be reused
            in future processing, providing 90% performance improvement.

        Input Requirements:
            - state.decomposed_modules: Enhanced modules from decomposition
            - state.execution_source: Source of execution (enhancement vs registry)
            - state.migration_name: For registry path construction
            - state.cloud_category: For path construction

        Output:
            - registry_updated: Boolean indicating if registry was updated
            - saved_modules: List of module names saved to registry

        Next Nodes:
            - Always → enhancement_iteration_control (continue workflow)
        """
        try:
            execution_source = getattr(state, 'execution_source', 'enhancement')
            decomposed_modules = getattr(state, 'decomposed_modules', [])
            migration_name = state.migration_name
            cloud_category = state.cloud_category

            print(f"💾 Save Enhanced to Registry:")
            print(f"   - Execution source: {execution_source}")
            print(f"   - Modules to check: {len(decomposed_modules)}")

            saved_modules = []
            skipped_modules = []

            # Only save if this came from enhancement (not registry)
            # Save enhanced modules that passed AI comparison
            if execution_source in ['enhancement', 'registry_failed', 'mixed_failed'] and decomposed_modules:
                print(f"🔄 Saving enhanced modules to registry (source: {execution_source})...")

                for module_data in decomposed_modules:
                    if isinstance(module_data, dict):
                        module_name = module_data.get('module_name', '')
                        original_path = module_data.get('original_path', '')
                        has_functional_changes = module_data.get('has_functional_changes', False)
                        enhanced_path = module_data.get('enhanced_path', '')

                        # Only save modules that were actually changed/enhanced
                        if has_functional_changes and enhanced_path and original_path:
                            # Read the latest attempt file (already saved with attempt number)
                            try:
                                with open(enhanced_path, 'r', encoding='utf-8') as file:
                                    enhanced_code = file.read()

                                # Save to registry with original module name (without attempt number)
                                success = save_enhanced_module_to_registry(
                                    module_name,
                                    enhanced_code,
                                    original_path,
                                    migration_name,
                                    cloud_category
                                )

                                if success:
                                    # 🔄 UPDATE DATABASE: Set registry fields after successful file system save
                                    # Get absolute path for file operations, then convert to relative for database
                                    registry_absolute_path = get_registry_path(original_path, migration_name, cloud_category)

                                    # Convert to relative path for database storage (from Stage1_Metadata onwards)
                                    if cloud_category.lower() == 'local':
                                        base_path = Config.Qbook_Local_Path
                                    else:
                                        base_path = Config.Qbook_Path
                                    registry_relative_path = os.path.relpath(registry_absolute_path, base_path)
                                    registry_relative_path = registry_relative_path.replace("\\", "/")

                                    # Get module_id from decomposed_modules data
                                    module_id = module_data.get('module_id', None)

                                    if module_id:
                                        # Update only the 3 registry fields in database
                                        db_update_success = update_stage2_conversion_module_status(
                                            db_data=get_qbook_db_config(),
                                            module_id=module_id,
                                            registry_module_code=enhanced_code,     # Save enhanced code to DB
                                            is_inregistry=True,                     # Mark as in registry
                                            registry_path=registry_relative_path    # Set registry file path (RELATIVE)
                                        )

                                        if db_update_success:
                                            print(f"✅ Database updated for registry save: {module_name}")
                                        else:
                                            print(f"⚠️ Registry file saved but database update failed: {module_name}")
                                    else:
                                        print(f"⚠️ No module_id found for database update: {module_name}")

                                    saved_modules.append(module_name)
                                    print(f"✅ Enhanced module saved to registry: {module_name}")
                                else:
                                    print(f"❌ Failed to save enhanced module to registry: {module_name}")
                            except Exception as e:
                                print(f"❌ Failed to read enhanced module file {enhanced_path}: {str(e)}")
                        else:
                            skipped_modules.append(module_name)
                            print(f"⏭️ Skipping unchanged module: {module_name}")

                print(f"💾 Registry save complete: {len(saved_modules)}/{len(decomposed_modules)} modules saved")

            else:
                if execution_source in ['registry', 'mixed']:
                    print(f"⏭️ No save needed - direct {execution_source} execution succeeded")
                else:
                    print(f"⚠️ No modules to save - execution source: {execution_source}")

            # Excel logging following existing patterns
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                # Get source statement number using existing function
                source_statement_number = self.get_current_source_statement_number(state)

                # Log to Pipeline_Overview
                if execution_source in ['enhancement', 'registry_failed', 'mixed_failed'] and decomposed_modules:
                    # Enhancement success - log save results
                    self.log_pipeline_overview(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'total_modules': len(decomposed_modules),
                        'pre_count': 0,
                        'responsible_count': len(saved_modules),
                        'post_count': 0,
                        'overall_status': 'REGISTRY_SAVE_COMPLETE',
                        'pipeline_phase': 'Registry Save',
                        'success': len(saved_modules) > 0,
                        'notes': f"Registry save results: {len(saved_modules)} saved, {len(skipped_modules)} skipped (unchanged), {len(decomposed_modules)} total. Execution source: {execution_source}"
                    })
                else:
                    # Direct execution success - log no save needed
                    self.log_pipeline_overview(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'total_modules': 0,
                        'pre_count': 0,
                        'responsible_count': 0,
                        'post_count': 0,
                        'overall_status': 'REGISTRY_SAVE_SKIPPED',
                        'pipeline_phase': 'Registry Save Decision',
                        'success': True,
                        'notes': f"No registry save needed - direct {execution_source} execution succeeded. Modules already working as-is."
                    })

                # Log module-level details only if there were modules to process
                if execution_source in ['enhancement', 'registry_failed', 'mixed_failed'] and decomposed_modules:
                    # Log each saved module
                    for module_name in saved_modules:
                        self.log_module_processing(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'operation': 'REGISTRY_SAVE',
                        'module_name': module_name,
                        'module_type': 'enhanced',
                        'input_code': 'Enhanced module code',
                        'output_code': 'Saved to registry',
                        'status': 'SAVED_TO_REGISTRY',
                        'changes_made': True,
                        'module_order': saved_modules.index(module_name) + 1,
                        'additional_info': f"Enhanced module saved to registry for future reuse"
                    })

                    # Small delay to prevent Excel file corruption
                    time.sleep(0.05)

                    # Log each skipped module (unchanged modules)
                    for module_name in skipped_modules:
                        self.log_module_processing(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'operation': 'REGISTRY_SKIP',
                        'module_name': module_name,
                        'module_type': 'enhanced',
                        'input_code': 'Enhanced module code',
                        'output_code': 'No changes detected',
                        'status': 'SKIPPED_UNCHANGED',
                        'changes_made': False,
                        'module_order': skipped_modules.index(module_name) + 1,
                        'additional_info': f"Module unchanged by AI enhancement - not saved to registry"
                        })

                        # Small delay to prevent Excel file corruption
                        time.sleep(0.05)
                else:
                    # Direct execution success - log that no save was needed
                    self.log_module_processing(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'source_statement_number': source_statement_number,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'operation': 'REGISTRY_SAVE_DECISION',
                        'module_name': 'N/A',
                        'module_type': 'N/A',
                        'input_code': f"Direct {execution_source} execution",
                        'output_code': 'No save needed',
                        'status': 'SAVE_SKIPPED',
                        'changes_made': False,
                        'module_order': 1,
                        'additional_info': f"Direct {execution_source} execution succeeded - modules working as-is, no registry save needed"
                    })

            return {
                "registry_updated": len(saved_modules) > 0,
                "saved_modules": saved_modules,
                "skipped_modules": skipped_modules,
                "total_modules_processed": len(decomposed_modules)
            }

        except Exception as e:
            print(f"❌ Error saving enhanced modules to registry: {str(e)}")
            return {
                "registry_updated": False,
                "saved_modules": [],
                "skipped_modules": [],
                "total_modules_processed": 0,
                "error": str(e)
            }


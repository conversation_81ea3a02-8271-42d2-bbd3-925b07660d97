import sys
import traceback
from datetime import datetime


class Stage2Exception(Exception):
    """Enhanced exception for Stage2 pipeline with better logging support."""

    def __init__(self, error_message, error_details=None, feature_name=None, context=None):
        super().__init__(error_message)

        self.error_message = str(error_message)
        self.feature_name = feature_name
        self.context = context or {}
        self.timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Handle error details
        if error_details and hasattr(error_details, 'exc_info'):
            # Traditional sys-based error details
            _, _, exc_tb = error_details.exc_info()
            if exc_tb:
                self.file_name = exc_tb.tb_frame.f_code.co_filename
                self.lineno = exc_tb.tb_lineno
                self.traceback_str = ''.join(traceback.format_exception(*error_details.exc_info()))
            else:
                self.file_name = "Unknown"
                self.lineno = 0
                self.traceback_str = "No traceback available"
        else:
            # Direct exception or no error details
            self.file_name = "Unknown"
            self.lineno = 0
            self.traceback_str = ''.join(traceback.format_stack())

    def to_log_format(self):
        """Convert exception to detailed log format."""
        return f"""
Timestamp: {self.timestamp}
Feature: {self.feature_name or 'N/A'}
File: {self.file_name}
Line: {self.lineno}
Message: {self.error_message}
Context: {self.context}
Traceback:
{self.traceback_str}
"""

    def __str__(self):
        return f"[Stage2Exception] {self.error_message}"


class FeatureExecutionException(Stage2Exception):
    """Specific exception for feature execution failures."""

    def __init__(self, feature_name, error_message, input_data=None, module_path=None, original_exception=None):
        context = {
            "module_path": module_path,
            "input_data_length": len(str(input_data)) if input_data else 0,
            "original_exception": str(original_exception) if original_exception else None
        }

        super().__init__(
            error_message=f"Feature '{feature_name}' execution failed: {error_message}",
            feature_name=feature_name,
            context=context
        )

        self.input_data = input_data
        self.module_path = module_path
        self.original_exception = original_exception


# Maintain backward compatibility
DocumentPortalException = Stage2Exception
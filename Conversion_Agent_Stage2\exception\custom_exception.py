import sys
import traceback
from datetime import datetime


class DocumentPortalException(Exception):
    def __init__(self, error_message, error_details):
        _, _, exc_tb = error_details.exc_info()
        self.file_name = exc_tb.tb_frame.f_code.co_filename
        self.lineno = exc_tb.tb_lineno
        self.error_message = str(error_message)
        self.traceback_str = ''.join(traceback.format_exception(*error_details.exc_info()))

    def __str__(self):
        return f"""
        Error in [{self.file_name}] at line [{self.lineno}]
        Message: {self.error_message}
        Traceback:
        {self.traceback_str}
        """


class Stage2PipelineException(Exception):
    """
    Custom exception for Stage2 pipeline processing with enhanced logging capabilities.
    """

    def __init__(self, error_message, context=None, feature_name=None, statement_number=None,
                 attempt_number=None, error_type="PIPELINE_ERROR", original_exception=None):
        """
        Initialize Stage2 pipeline exception.

        Args:
            error_message: Main error message
            context: Additional context information
            feature_name: Name of the feature where error occurred
            statement_number: Statement number being processed
            attempt_number: Current attempt number
            error_type: Type of error (PIPELINE_ERROR, FEATURE_EXECUTION_ERROR, etc.)
            original_exception: Original exception if this is a wrapper
        """
        super().__init__(error_message)

        self.error_message = str(error_message)
        self.context = context or {}
        self.feature_name = feature_name
        self.statement_number = statement_number
        self.attempt_number = attempt_number
        self.error_type = error_type
        self.original_exception = original_exception
        self.timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Capture traceback information
        if original_exception:
            self.traceback_str = ''.join(traceback.format_exception(
                type(original_exception), original_exception, original_exception.__traceback__
            ))
        else:
            self.traceback_str = ''.join(traceback.format_stack())

    def to_log_format(self):
        """Convert exception to detailed log format."""
        log_entry = f"""
{'!'*80}
STAGE2 PIPELINE EXCEPTION
{'!'*80}
Error Type: {self.error_type}
Timestamp: {self.timestamp}
Statement Number: {self.statement_number or 'N/A'}
Attempt Number: {self.attempt_number or 'N/A'}
Feature Name: {self.feature_name or 'N/A'}

Error Message:
{'-'*40}
{self.error_message}

Context Information:
{'-'*40}
{self._format_context()}

Traceback:
{'-'*40}
{self.traceback_str}
{'!'*80}
"""
        return log_entry

    def _format_context(self):
        """Format context information for logging."""
        if not self.context:
            return "No additional context available"

        formatted_context = []
        for key, value in self.context.items():
            formatted_context.append(f"{key}: {value}")

        return "\n".join(formatted_context)

    def __str__(self):
        return f"[{self.error_type}] {self.error_message}"


class FeatureExecutionException(Stage2PipelineException):
    """Specific exception for feature execution failures."""

    def __init__(self, feature_name, error_message, input_data=None, module_path=None,
                 statement_number=None, attempt_number=None, original_exception=None):
        context = {
            "module_path": module_path,
            "input_data_length": len(str(input_data)) if input_data else 0,
            "input_preview": str(input_data)[:200] + "..." if input_data and len(str(input_data)) > 200 else str(input_data)
        }

        super().__init__(
            error_message=error_message,
            context=context,
            feature_name=feature_name,
            statement_number=statement_number,
            attempt_number=attempt_number,
            error_type="FEATURE_EXECUTION_ERROR",
            original_exception=original_exception
        )


class PipelineInitializationException(Stage2PipelineException):
    """Exception for pipeline initialization failures."""

    def __init__(self, error_message, migration_name=None, object_name=None,
                 statement_number=None, attempt_number=None, original_exception=None):
        context = {
            "migration_name": migration_name,
            "object_name": object_name
        }

        super().__init__(
            error_message=error_message,
            context=context,
            statement_number=statement_number,
            attempt_number=attempt_number,
            error_type="PIPELINE_INITIALIZATION_ERROR",
            original_exception=original_exception
        )


class LoggerException(Stage2PipelineException):
    """Exception for logging-related failures."""

    def __init__(self, error_message, log_file_path=None, original_exception=None):
        context = {
            "log_file_path": log_file_path
        }

        super().__init__(
            error_message=error_message,
            context=context,
            error_type="LOGGER_ERROR",
            original_exception=original_exception
        )


if __name__ == "__main__":
    try:
        a = 1 / 0  # deliberate error
    except Exception as e:
        app_exc = DocumentPortalException(e, sys)
        #logger.error(app_exc)  # log it to file
        raise app_exc  # propagate with full traceback

    # Example of Stage2 pipeline exception
    try:
        raise FeatureExecutionException(
            feature_name="nvl",
            error_message="Failed to execute NVL conversion",
            input_data="SELECT NVL(column1, 'default') FROM table1",
            module_path="/path/to/nvl.py",
            statement_number=5,
            attempt_number=2
        )
    except Stage2PipelineException as e:
        print(e.to_log_format())
import logging
import os
from datetime import datetime
from config import Config
from Conversion_Agent_Stage2.exception.custom_exception import Stage2Exception


class CustomLogger:
    def __init__(self, log_dir="logs"):
        # Ensure logs directory exists
        self.logs_dir = os.path.join(os.getcwd(), log_dir)
        os.makedirs(self.logs_dir, exist_ok=True)

        # Create timestamped log file
        log_file = f"{datetime.now().strftime('%m_%d_%Y_%H_%M_%S')}.log"
        self.log_file_path = os.path.join(self.logs_dir, log_file)

    def get_logger(self, name=__file__):
        """
        Returns a logger instance with file + console handlers.
        Default name is the current file name (without path).
        """
        logger_name = os.path.basename(name)
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.INFO)

        # Formatter for both handlers
        file_formatter = logging.Formatter(
            "[ %(asctime)s ] %(levelname)s %(name)s (line:%(lineno)d) - %(message)s"
        )
        console_formatter = logging.Formatter(
            "[ %(levelname)s ] %(message)s"
        )

        # File handler (logs saved to file)
        file_handler = logging.FileHandler(self.log_file_path)
        file_handler.setFormatter(file_formatter)

        # Console handler (logs printed on terminal)
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(console_formatter)

        # Avoid duplicate handlers if logger is reused
        if not logger.handlers:
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)

        return logger


class Stage2PipelineLogger:
    """
    Enhanced logger for Stage2 pipeline processing.
    Creates comprehensive log files in QBook path without disrupting code flow.
    """

    def __init__(self, cloud_category, migration_name, object_name, statement_number, attempt_number):
        """Initialize pipeline logger for specific statement/attempt."""
        self.cloud_category = cloud_category
        self.migration_name = migration_name
        self.object_name = object_name
        self.statement_number = statement_number
        self.attempt_number = attempt_number

        # Determine QBook path
        if cloud_category.lower() == 'local':
            self.qbook_path = Config.Qbook_Local_Path
        else:
            self.qbook_path = Config.Qbook_Path

        # Create log directories
        self.log_dir = os.path.join(self.qbook_path, "Stage2_Pipeline_Logs", migration_name, object_name)
        self.exceptions_dir = os.path.join(self.log_dir, "exceptions")
        os.makedirs(self.log_dir, exist_ok=True)
        os.makedirs(self.exceptions_dir, exist_ok=True)

        # Create log file paths
        log_filename = f"statement_{statement_number}_attempt_{attempt_number}.log"
        self.log_file_path = os.path.join(self.log_dir, log_filename)

        exception_filename = f"statement_{statement_number}_attempt_{attempt_number}_exceptions.log"
        self.exception_log_path = os.path.join(self.exceptions_dir, exception_filename)

        # Initialize log content
        self.log_entries = []
        self.exception_entries = []
        self._initialize_logs()

    def _initialize_logs(self):
        """Initialize log files with headers."""
        # Main log header
        main_header = f"""{'='*80}
STAGE2 PIPELINE EXECUTION LOG
{'='*80}
Migration: {self.migration_name}
Object: {self.object_name}
Statement Number: {self.statement_number}
Attempt Number: {self.attempt_number}
Cloud Category: {self.cloud_category}
Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'='*80}

"""
        self.log_entries.append(main_header)

        # Exception log header
        exception_header = f"""{'='*80}
STAGE2 PIPELINE EXCEPTION LOG
{'='*80}
Migration: {self.migration_name}
Object: {self.object_name}
Statement Number: {self.statement_number}
Attempt Number: {self.attempt_number}
Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'='*80}

"""
        self.exception_entries.append(exception_header)
        self._write_logs()

    def log_section(self, title, content, level="INFO"):
        """Log a section with title and content."""
        timestamp = datetime.now().strftime('%H:%M:%S')
        section = f"""[{timestamp}] [{level}] {title}
{'-'*60}
{content}
{'-'*60}

"""
        self.log_entries.append(section)
        self._write_logs()

    def log_original_input(self, original_statement, ai_converted_statement, target_statement=None, deployment_error=None):
        """Log original input data."""
        content = f"""Original Source Statement:
{original_statement or 'N/A'}

AI Converted Statement:
{ai_converted_statement or 'N/A'}

Original Target Statement:
{target_statement or 'N/A'}

Deployment Error:
{deployment_error or 'N/A'}"""

        self.log_section("ORIGINAL INPUT DATA", content)

    def log_post_typecasting(self, typecasted_data):
        """Log data after typecasting."""
        self.log_section("POST-TYPECASTING DATA", typecasted_data or 'N/A')

    def log_feature_execution(self, feature_name, feature_type, input_data, output_data,
                            execution_status="Success", error_message=None, module_path=None):
        """Log individual feature execution."""
        status_icon = "✅" if execution_status == "Success" else "❌"

        content = f"""Feature: {feature_name}
Type: {feature_type}
Status: {status_icon} {execution_status}
Module Path: {module_path or 'N/A'}

Input Data:
{str(input_data)[:500] + '...' if len(str(input_data)) > 500 else str(input_data)}

Output Data:
{str(output_data)[:500] + '...' if len(str(output_data)) > 500 else str(output_data)}"""

        if error_message:
            content += f"\n\nError Message:\n{error_message}"

        self.log_section(f"FEATURE EXECUTION: {feature_name.upper()}", content,
                        "INFO" if execution_status == "Success" else "ERROR")

    def log_pipeline_summary(self, final_output, total_features, successful_features, failed_features, overall_status):
        """Log pipeline execution summary."""
        status_icon = "✅" if overall_status == "Success" else "❌"

        content = f"""Overall Status: {status_icon} {overall_status}
Total Features Executed: {total_features}
Successful Features: {successful_features}
Failed Features: {failed_features}
Completion Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Final Output:
{str(final_output)[:1000] + '...' if len(str(final_output)) > 1000 else str(final_output)}"""

        self.log_section("PIPELINE EXECUTION SUMMARY", content,
                        "INFO" if overall_status == "Success" else "ERROR")

    def log_exception(self, exception, context=None, feature_name=None):
        """Log exception to both main and exception logs."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Handle Stage2Exception
        if isinstance(exception, Stage2Exception):
            exception_detail = f"""[{timestamp}] STAGE2 EXCEPTION
Feature: {feature_name or 'N/A'}
Context: {context or 'N/A'}
File: {exception.file_name}
Line: {exception.lineno}
Message: {exception.error_message}
Traceback:
{exception.traceback_str}
{'='*60}

"""
        else:
            # Handle regular exceptions
            import traceback
            exception_detail = f"""[{timestamp}] STANDARD EXCEPTION
Feature: {feature_name or 'N/A'}
Context: {context or 'N/A'}
Type: {type(exception).__name__}
Message: {str(exception)}
Traceback:
{''.join(traceback.format_exception(type(exception), exception, exception.__traceback__))}
{'='*60}

"""

        # Add to exception log
        self.exception_entries.append(exception_detail)

        # Add summary to main log
        summary = f"""Exception occurred in feature: {feature_name or 'N/A'}
Type: {type(exception).__name__}
Message: {str(exception)}
Details: See exceptions/{os.path.basename(self.exception_log_path)}"""

        self.log_section("EXCEPTION OCCURRED", summary, "ERROR")
        self._write_logs()

    def log_custom_message(self, message, level="INFO"):
        """Log custom message."""
        timestamp = datetime.now().strftime('%H:%M:%S')
        entry = f"[{timestamp}] [{level}] {message}\n\n"
        self.log_entries.append(entry)
        self._write_logs()

    def _write_logs(self):
        """Write logs to files."""
        try:
            # Write main log
            with open(self.log_file_path, 'w', encoding='utf-8') as f:
                f.writelines(self.log_entries)

            # Write exception log if there are exceptions
            if len(self.exception_entries) > 1:  # More than just header
                with open(self.exception_log_path, 'w', encoding='utf-8') as f:
                    f.writelines(self.exception_entries)

        except Exception as e:
            # Fallback - at least try to print the error
            print(f"❌ Error writing to log files: {str(e)}")

    def get_log_file_path(self):
        """Return the path to the main log file."""
        return self.log_file_path

    def get_exception_log_path(self):
        """Return the path to the exception log file."""
        return self.exception_log_path
import logging
import os
from datetime import datetime
from config import Config


class CustomLogger:
    def __init__(self, log_dir="logs"):
        # Ensure logs directory exists
        self.logs_dir = os.path.join(os.getcwd(), log_dir)
        os.makedirs(self.logs_dir, exist_ok=True)

        # Create timestamped log file
        log_file = f"{datetime.now().strftime('%m_%d_%Y_%H_%M_%S')}.log"
        self.log_file_path = os.path.join(self.logs_dir, log_file)

    def get_logger(self, name=__file__):
        """
        Returns a logger instance with file + console handlers.
        Default name is the current file name (without path).
        """
        logger_name = os.path.basename(name)
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.INFO)

        # Formatter for both handlers
        file_formatter = logging.Formatter(
            "[ %(asctime)s ] %(levelname)s %(name)s (line:%(lineno)d) - %(message)s"
        )
        console_formatter = logging.Formatter(
            "[ %(levelname)s ] %(message)s"
        )

        # File handler (logs saved to file)
        file_handler = logging.FileHandler(self.log_file_path)
        file_handler.setFormatter(file_formatter)

        # Console handler (logs printed on terminal)
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(console_formatter)

        # Avoid duplicate handlers if logger is reused
        if not logger.handlers:
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)

        return logger


class Stage2PipelineLogger:
    """
    Enhanced logger for Stage2 pipeline processing.
    Creates one log file per statement/attempt in QBook path for developer debugging.
    """

    def __init__(self, cloud_category, migration_name, object_name, statement_number, attempt_number):
        """
        Initialize pipeline logger for specific statement/attempt.

        Args:
            cloud_category: 'local' or 'cloud' to determine QBook path
            migration_name: Migration name (e.g., Oracle_Postgres14)
            object_name: Name of the object being processed
            statement_number: Statement number being processed
            attempt_number: Current attempt number (1-5)
        """
        self.cloud_category = cloud_category
        self.migration_name = migration_name
        self.object_name = object_name
        self.statement_number = statement_number
        self.attempt_number = attempt_number

        # Determine QBook path based on cloud category
        if cloud_category.lower() == 'local':
            self.qbook_path = Config.Qbook_Local_Path
        else:
            self.qbook_path = Config.Qbook_Path

        # Create log directory in QBook path
        self.log_dir = os.path.join(self.qbook_path, "Stage2_Pipeline_Logs", migration_name, object_name)
        os.makedirs(self.log_dir, exist_ok=True)

        # Create log file name: statement_{number}_attempt_{attempt}.log
        log_filename = f"statement_{statement_number}_attempt_{attempt_number}.log"
        self.log_file_path = os.path.join(self.log_dir, log_filename)

        # Initialize log content
        self.log_content = []
        self._initialize_log()

    def _initialize_log(self):
        """Initialize log file with header information."""
        header = f"""
{'='*80}
STAGE2 PIPELINE EXECUTION LOG
{'='*80}
Migration: {self.migration_name}
Object: {self.object_name}
Statement Number: {self.statement_number}
Attempt Number: {self.attempt_number}
Cloud Category: {self.cloud_category}
Log File: {self.log_file_path}
Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'='*80}

"""
        self.log_content.append(header)
        self._write_to_file()

    def log_original_input(self, original_statement, ai_converted_statement, target_statement=None, deployment_error=None):
        """Log the original input data for the statement."""
        section = f"""
{'='*60}
ORIGINAL INPUT DATA
{'='*60}
Original Source Statement:
{'-'*30}
{original_statement or 'N/A'}

AI Converted Statement:
{'-'*30}
{ai_converted_statement or 'N/A'}

Original Target Statement:
{'-'*30}
{target_statement or 'N/A'}

Deployment Error (if any):
{'-'*30}
{deployment_error or 'N/A'}
{'='*60}

"""
        self.log_content.append(section)
        self._write_to_file()
        print(f"📝 Logged original input data for statement {self.statement_number}")

    def log_post_typecasting(self, typecasted_data):
        """Log data after typecasting operations."""
        section = f"""
{'='*60}
POST-TYPECASTING DATA
{'='*60}
{typecasted_data or 'N/A'}
{'='*60}

"""
        self.log_content.append(section)
        self._write_to_file()
        print(f"📝 Logged post-typecasting data for statement {self.statement_number}")

    def log_feature_execution(self, feature_name, feature_type, input_data, output_data, execution_status="Success", error_message=None):
        """
        Log individual feature execution with input/output.

        Args:
            feature_name: Name of the feature being executed
            feature_type: Type of feature (pre, responsible, post)
            input_data: Input data to the feature
            output_data: Output data from the feature
            execution_status: Success/Failed
            error_message: Error message if execution failed
        """
        status_icon = "✅" if execution_status == "Success" else "❌"

        section = f"""
{'-'*60}
FEATURE EXECUTION: {feature_name.upper()}
{'-'*60}
Feature Type: {feature_type}
Status: {status_icon} {execution_status}
Execution Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Input Data:
{'-'*20}
{input_data or 'N/A'}

Output Data:
{'-'*20}
{output_data or 'N/A'}
"""

        if error_message:
            section += f"""
Error Message:
{'-'*20}
{error_message}
"""

        section += f"{'-'*60}\n\n"

        self.log_content.append(section)
        self._write_to_file()
        print(f"📝 Logged {feature_type} feature execution: {feature_name} - {execution_status}")

    def log_pipeline_summary(self, final_output, total_features_executed, successful_features, failed_features, overall_status):
        """Log pipeline execution summary."""
        status_icon = "✅" if overall_status == "Success" else "❌"

        section = f"""
{'='*60}
PIPELINE EXECUTION SUMMARY
{'='*60}
Overall Status: {status_icon} {overall_status}
Total Features Executed: {total_features_executed}
Successful Features: {successful_features}
Failed Features: {failed_features}
Completion Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Final Output:
{'-'*30}
{final_output or 'N/A'}
{'='*60}

"""
        self.log_content.append(section)
        self._write_to_file()
        print(f"📝 Logged pipeline summary for statement {self.statement_number} - {overall_status}")

    def log_error(self, error_type, error_message, context=None):
        """Log error information."""
        section = f"""
{'!'*60}
ERROR: {error_type.upper()}
{'!'*60}
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Message: {error_message}

Context:
{'-'*20}
{context or 'N/A'}
{'!'*60}

"""
        self.log_content.append(section)
        self._write_to_file()
        print(f"❌ Logged error: {error_type}")

    def log_custom_message(self, message, level="INFO"):
        """Log custom message with timestamp."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        formatted_message = f"[{timestamp}] [{level}] {message}\n\n"

        self.log_content.append(formatted_message)
        self._write_to_file()
        print(f"📝 Logged custom message: {message}")

    def _write_to_file(self):
        """Write current log content to file."""
        try:
            with open(self.log_file_path, 'w', encoding='utf-8') as f:
                f.writelines(self.log_content)
        except Exception as e:
            print(f"❌ Error writing to log file {self.log_file_path}: {str(e)}")

    def get_log_file_path(self):
        """Return the path to the log file."""
        return self.log_file_path

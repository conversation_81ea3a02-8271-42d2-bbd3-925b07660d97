import logging
import os
from datetime import datetime
from config import Config
from Conversion_Agent_Stage2.exception.custom_exception import Stage2PipelineException, FeatureExecutionException, LoggerException


class CustomLogger:
    def __init__(self, log_dir="logs"):
        # Ensure logs directory exists
        self.logs_dir = os.path.join(os.getcwd(), log_dir)
        os.makedirs(self.logs_dir, exist_ok=True)

        # Create timestamped log file
        log_file = f"{datetime.now().strftime('%m_%d_%Y_%H_%M_%S')}.log"
        self.log_file_path = os.path.join(self.logs_dir, log_file)

    def get_logger(self, name=__file__):
        """
        Returns a logger instance with file + console handlers.
        Default name is the current file name (without path).
        """
        logger_name = os.path.basename(name)
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.INFO)

        # Formatter for both handlers
        file_formatter = logging.Formatter(
            "[ %(asctime)s ] %(levelname)s %(name)s (line:%(lineno)d) - %(message)s"
        )
        console_formatter = logging.Formatter(
            "[ %(levelname)s ] %(message)s"
        )

        # File handler (logs saved to file)
        file_handler = logging.FileHandler(self.log_file_path)
        file_handler.setFormatter(file_formatter)

        # Console handler (logs printed on terminal)
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(console_formatter)

        # Avoid duplicate handlers if logger is reused
        if not logger.handlers:
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)

        return logger


class Stage2PipelineLogger:
    """
    Enhanced logger for Stage2 pipeline processing.
    Creates one log file per statement/attempt in QBook path for developer debugging.
    """

    def __init__(self, cloud_category, migration_name, object_name, statement_number, attempt_number):
        """
        Initialize pipeline logger for specific statement/attempt.

        Args:
            cloud_category: 'local' or 'cloud' to determine QBook path
            migration_name: Migration name (e.g., Oracle_Postgres14)
            object_name: Name of the object being processed
            statement_number: Statement number being processed
            attempt_number: Current attempt number (1-5)
        """
        self.cloud_category = cloud_category
        self.migration_name = migration_name
        self.object_name = object_name
        self.statement_number = statement_number
        self.attempt_number = attempt_number

        # Determine QBook path based on cloud category
        if cloud_category.lower() == 'local':
            self.qbook_path = Config.Qbook_Local_Path
        else:
            self.qbook_path = Config.Qbook_Path

        # Create log directory in QBook path
        self.log_dir = os.path.join(self.qbook_path, "Stage2_Pipeline_Logs", migration_name, object_name)
        os.makedirs(self.log_dir, exist_ok=True)

        # Create exceptions directory for error logs
        self.exceptions_dir = os.path.join(self.log_dir, "exceptions")
        os.makedirs(self.exceptions_dir, exist_ok=True)

        # Create log file name: statement_{number}_attempt_{attempt}.log
        log_filename = f"statement_{statement_number}_attempt_{attempt_number}.log"
        self.log_file_path = os.path.join(self.log_dir, log_filename)

        # Create exception log file name
        exception_filename = f"statement_{statement_number}_attempt_{attempt_number}_exceptions.log"
        self.exception_log_path = os.path.join(self.exceptions_dir, exception_filename)

        # Initialize log content
        self.log_content = []
        self.exception_content = []
        self._initialize_log()
        self._initialize_exception_log()

    def _initialize_log(self):
        """Initialize log file with header information."""
        header = f"""
{'='*80}
STAGE2 PIPELINE EXECUTION LOG
{'='*80}
Migration: {self.migration_name}
Object: {self.object_name}
Statement Number: {self.statement_number}
Attempt Number: {self.attempt_number}
Cloud Category: {self.cloud_category}
Log File: {self.log_file_path}
Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'='*80}

"""
        self.log_content.append(header)
        self._write_to_file()

    def _initialize_exception_log(self):
        """Initialize exception log file with header information."""
        header = f"""
{'='*80}
STAGE2 PIPELINE EXCEPTION LOG
{'='*80}
Migration: {self.migration_name}
Object: {self.object_name}
Statement Number: {self.statement_number}
Attempt Number: {self.attempt_number}
Cloud Category: {self.cloud_category}
Exception Log File: {self.exception_log_path}
Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'='*80}

"""
        self.exception_content.append(header)
        self._write_exception_to_file()

    def log_original_input(self, original_statement, ai_converted_statement, target_statement=None, deployment_error=None):
        """Log the original input data for the statement."""
        section = f"""
{'='*60}
ORIGINAL INPUT DATA
{'='*60}
Original Source Statement:
{'-'*30}
{original_statement or 'N/A'}

AI Converted Statement:
{'-'*30}
{ai_converted_statement or 'N/A'}

Original Target Statement:
{'-'*30}
{target_statement or 'N/A'}

Deployment Error (if any):
{'-'*30}
{deployment_error or 'N/A'}
{'='*60}

"""
        self.log_content.append(section)
        self._write_to_file()
        print(f"📝 Logged original input data for statement {self.statement_number}")

    def log_post_typecasting(self, typecasted_data):
        """Log data after typecasting operations."""
        section = f"""
{'='*60}
POST-TYPECASTING DATA
{'='*60}
{typecasted_data or 'N/A'}
{'='*60}

"""
        self.log_content.append(section)
        self._write_to_file()
        print(f"📝 Logged post-typecasting data for statement {self.statement_number}")

    def log_feature_execution(self, feature_name, feature_type, input_data, output_data, execution_status="Success", error_message=None):
        """
        Log individual feature execution with input/output.

        Args:
            feature_name: Name of the feature being executed
            feature_type: Type of feature (pre, responsible, post)
            input_data: Input data to the feature
            output_data: Output data from the feature
            execution_status: Success/Failed
            error_message: Error message if execution failed
        """
        status_icon = "✅" if execution_status == "Success" else "❌"

        section = f"""
{'-'*60}
FEATURE EXECUTION: {feature_name.upper()}
{'-'*60}
Feature Type: {feature_type}
Status: {status_icon} {execution_status}
Execution Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Input Data:
{'-'*20}
{input_data or 'N/A'}

Output Data:
{'-'*20}
{output_data or 'N/A'}
"""

        if error_message:
            section += f"""
Error Message:
{'-'*20}
{error_message}
"""

        section += f"{'-'*60}\n\n"

        self.log_content.append(section)
        self._write_to_file()
        print(f"📝 Logged {feature_type} feature execution: {feature_name} - {execution_status}")

    def log_pipeline_summary(self, final_output, total_features_executed, successful_features, failed_features, overall_status):
        """Log pipeline execution summary."""
        status_icon = "✅" if overall_status == "Success" else "❌"

        section = f"""
{'='*60}
PIPELINE EXECUTION SUMMARY
{'='*60}
Overall Status: {status_icon} {overall_status}
Total Features Executed: {total_features_executed}
Successful Features: {successful_features}
Failed Features: {failed_features}
Completion Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Final Output:
{'-'*30}
{final_output or 'N/A'}
{'='*60}

"""
        self.log_content.append(section)
        self._write_to_file()
        print(f"📝 Logged pipeline summary for statement {self.statement_number} - {overall_status}")

    def log_error(self, error_type, error_message, context=None):
        """Log error information."""
        section = f"""
{'!'*60}
ERROR: {error_type.upper()}
{'!'*60}
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Message: {error_message}

Context:
{'-'*20}
{context or 'N/A'}
{'!'*60}

"""
        self.log_content.append(section)
        self._write_to_file()
        print(f"❌ Logged error: {error_type}")

    def log_exception(self, exception, feature_name=None, additional_context=None):
        """
        Log exception information to both main log and exception log.

        Args:
            exception: Exception object (preferably Stage2PipelineException)
            feature_name: Name of feature where exception occurred
            additional_context: Additional context information
        """
        try:
            # Handle Stage2PipelineException with detailed logging
            if isinstance(exception, Stage2PipelineException):
                exception_log = exception.to_log_format()

                # Add to exception log
                self.exception_content.append(exception_log)
                self._write_exception_to_file()

                # Add summary to main log
                main_log_section = f"""
{'!'*60}
EXCEPTION OCCURRED: {exception.error_type}
{'!'*60}
Time: {exception.timestamp}
Feature: {feature_name or exception.feature_name or 'N/A'}
Message: {exception.error_message}
Details: See exceptions/{os.path.basename(self.exception_log_path)}
{'!'*60}

"""
                self.log_content.append(main_log_section)
                self._write_to_file()
                print(f"❌ Logged Stage2 exception: {exception.error_type} - {feature_name or exception.feature_name}")

            else:
                # Handle regular exceptions
                import traceback

                exception_details = f"""
{'!'*80}
STANDARD EXCEPTION
{'!'*80}
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Exception Type: {type(exception).__name__}
Feature: {feature_name or 'N/A'}
Statement: {self.statement_number}
Attempt: {self.attempt_number}

Message:
{'-'*40}
{str(exception)}

Additional Context:
{'-'*40}
{additional_context or 'N/A'}

Traceback:
{'-'*40}
{''.join(traceback.format_exception(type(exception), exception, exception.__traceback__))}
{'!'*80}

"""
                # Add to exception log
                self.exception_content.append(exception_details)
                self._write_exception_to_file()

                # Add summary to main log
                main_log_section = f"""
{'!'*60}
EXCEPTION OCCURRED: {type(exception).__name__}
{'!'*60}
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Feature: {feature_name or 'N/A'}
Message: {str(exception)}
Details: See exceptions/{os.path.basename(self.exception_log_path)}
{'!'*60}

"""
                self.log_content.append(main_log_section)
                self._write_to_file()
                print(f"❌ Logged exception: {type(exception).__name__} - {feature_name}")

        except Exception as log_exception:
            # Fallback logging if exception logging fails
            fallback_message = f"""
{'!'*60}
LOGGING EXCEPTION OCCURRED
{'!'*60}
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Original Exception: {str(exception)}
Logging Exception: {str(log_exception)}
Feature: {feature_name or 'N/A'}
{'!'*60}

"""
            try:
                self.log_content.append(fallback_message)
                self._write_to_file()
            except:
                print(f"❌ Critical: Failed to log exception - {str(exception)}")

    def log_feature_exception(self, feature_name, exception, input_data=None, module_path=None):
        """
        Log feature-specific exception with enhanced context.

        Args:
            feature_name: Name of the feature that failed
            exception: Exception that occurred
            input_data: Input data that caused the failure
            module_path: Path to the module that failed
        """
        # Create FeatureExecutionException if not already one
        if not isinstance(exception, FeatureExecutionException):
            feature_exception = FeatureExecutionException(
                feature_name=feature_name,
                error_message=str(exception),
                input_data=input_data,
                module_path=module_path,
                statement_number=self.statement_number,
                attempt_number=self.attempt_number,
                original_exception=exception
            )
        else:
            feature_exception = exception

        # Log the feature exception
        self.log_exception(feature_exception, feature_name)

    def log_custom_message(self, message, level="INFO"):
        """Log custom message with timestamp."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        formatted_message = f"[{timestamp}] [{level}] {message}\n\n"

        self.log_content.append(formatted_message)
        self._write_to_file()
        print(f"📝 Logged custom message: {message}")

    def _write_to_file(self):
        """Write current log content to file."""
        try:
            with open(self.log_file_path, 'w', encoding='utf-8') as f:
                f.writelines(self.log_content)
        except Exception as e:
            print(f"❌ Error writing to log file {self.log_file_path}: {str(e)}")

    def get_log_file_path(self):
        """Return the path to the log file."""
        return self.log_file_path
